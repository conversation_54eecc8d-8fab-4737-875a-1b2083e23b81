import { BadRequestException, Injectable, NotFoundException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model, PipelineStage, Types } from "mongoose";
import { FacilityAvailability } from "src/facility/schemas/facility-availability.schema";
import { Facility } from "src/facility/schemas/facility.schema";
import { StaffProfileDetails } from "src/staff/schemas/staff.schema";
import { User } from "src/users/schemas/user.schema";
import { TransactionService } from "src/utils/services/transaction.service";
import { TimeSlotsDTO } from "../dto/create-class-scheduling.dto";
import { CheckStaffAvailabilityDto } from "../dto/check-staff-availability.dto";
import { SchedulingPipe } from "../pipes/scheduling.pipe";
import { CreateSchedulingDto } from "../dto/create-scheduling.dto";
import { Pricing, Services } from "src/organization/schemas/pricing.schema";
import { Purchase, Suspensions } from "src/users/schemas/purchased-packages.schema";
import { Scheduling } from "../schemas/scheduling.schema";
import { Room } from "src/room/schema/room.schema";
import { SessionType } from "src/utils/enums/session-type.enum";
import { UpdateSchedulingDto } from "../dto/update-scheduling.dto";
import { ScheduleStatusType } from "../enums/schedule-status.enum";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { GetSchedulesDto } from "../dto/get-scheduling.dto";
import { format } from "date-fns";
import { StaffAvailability } from "src/staff/schemas/staff-availability";
import { StaffAvailabilityEnum } from "src/utils/enums/staff-availability.enum";
import { ClassType } from "src/utils/enums/class-type.enum";
import { PayRate } from "src/staff/schemas/pay-rate.schema";
import { MailService } from "src/mail/services/mail.service";
import { Clients } from "src/users/schemas/clients.schema";
import moment from "moment";
import { WaitTimeGatewayService } from "src/wait-time/gateway/wait-time-gateway.service";
import { IUserDocument } from "src/users/interfaces/user.interface";


@Injectable()
export class SchedulingService {
    constructor(
        @InjectModel(User.name) private UserModel: Model<User>,
        @InjectModel(Facility.name) private FacilityModel: Model<Facility>,
        @InjectModel(FacilityAvailability.name) private FacilityAvailabilityModel: Model<FacilityAvailability>,
        @InjectModel(StaffProfileDetails.name) private StaffProfileModel: Model<StaffProfileDetails>,
        @InjectModel(StaffAvailability.name) private StaffAvailabilityModel: Model<StaffAvailability>,
        @InjectModel(Services.name) private ServicesModel: Model<Services>,
        @InjectModel(Pricing.name) private PricingModel: Model<Pricing>,
        @InjectModel(Purchase.name) private PurchaseModel: Model<Purchase>,
        @InjectModel(PayRate.name) private PayRateModel: Model<PayRate>,
        @InjectModel(Room.name) private RoomModel: Model<Room>,
        @InjectModel(Scheduling.name) private SchedulingModel: Model<Scheduling>,
        @InjectModel(Clients.name) private ClientsModel: Model<Clients>,
        private readonly transactionService: TransactionService,
        private readonly schedulingPipe: SchedulingPipe,
        private readonly mailService: MailService,
        private waitTimeGatewayService: WaitTimeGatewayService,
    ) {}

    async findOrganization(checkStaffAvailabilityDto: CheckStaffAvailabilityDto): Promise<any> {
        let facilityDetails = await this.FacilityModel.findOne({ _id: checkStaffAvailabilityDto.facilityId }, { organizationId: 1 });
        if (!facilityDetails) throw new BadRequestException("Facility not found");
        return facilityDetails["organizationId"];
    }

    async availableStaff(checkStaffAvailabilityDto: CheckStaffAvailabilityDto, organizationId: string): Promise<any> {
        let availableDates = await this.findAvailableDatesInRange(checkStaffAvailabilityDto);
        if (availableDates.length == 0) throw new BadRequestException("Incorrect dates selected");
        let pipeline = await this.schedulingPipe.availableStaffPipe(checkStaffAvailabilityDto, organizationId, availableDates);
    }

    async findAvailableDatesInRange(checkStaffAvailabilityDto: CheckStaffAvailabilityDto) {
        const result: string[] = [];
        const start = new Date(checkStaffAvailabilityDto.fromDate);
        const end = new Date(checkStaffAvailabilityDto.endDate);

        const maxEndDate = new Date(start);
        maxEndDate.setMonth(start.getMonth() + 1);

        if (end > maxEndDate) {
            throw new BadRequestException("Date range cannot exceed 1 month limit");
        }

        // Map days of the week to keys in ScheduleDTO
        const daysMap = {
            0: checkStaffAvailabilityDto.schedule.sun,
            1: checkStaffAvailabilityDto.schedule.mon,
            2: checkStaffAvailabilityDto.schedule.tue,
            3: checkStaffAvailabilityDto.schedule.wed,
            4: checkStaffAvailabilityDto.schedule.thu,
            5: checkStaffAvailabilityDto.schedule.fri,
            6: checkStaffAvailabilityDto.schedule.sat,
        };

        // Normalize start and end times to 00:00:00 and 23:59:59
        start.setUTCHours(0, 0, 0, 0);
        end.setUTCHours(23, 59, 59, 999);

        while (start <= end) {
            const dayOfWeek = start.getUTCDay(); // Get day of the week (0=Sunday, ..., 6=Saturday)
            const timeSlots = daysMap[dayOfWeek];

            // Check if the array for the day is non-empty
            if (timeSlots && timeSlots.length > 0) {
                result.push(new Date(start).toISOString()); // Add the date in YYYY-MM-DD format
            }

            start.setUTCDate(start.getUTCDate() + 1); // Move to the next day
        }

        return result;
    }

    private validateNumberOfSessions(noOfSessions: number, timeSlots: any[]): void {
        if (noOfSessions < timeSlots.length) {
            throw new BadRequestException("No. of appointments cannot exceed the remaining number of sessions");
        }
    }

    private validateTimeSlots(timeSlots: any[]): void {
        for (const slot of timeSlots) {
            if (!this.isFromLessThanTo(slot)) {
                throw new BadRequestException(`Invalid time range: 'from' time (${slot.from}) must be less than 'to' time (${slot.to})`);
            }
        }

        const isOverlapping = (slot1, slot2) => {
            const date1 = new Date(slot1.date).toISOString().split("T")[0];
            const date2 = new Date(slot2.date).toISOString().split("T")[0];

            return slot1.from < slot2.to && slot2.from < slot1.to && date1 === date2;
        };

        for (let i = 0; i < timeSlots.length; i++) {
            for (let j = i + 1; j < timeSlots.length; j++) {
                if (isOverlapping(timeSlots[i], timeSlots[j])) {
                    throw new BadRequestException("Time slots overlap!");
                }
            }
        }
    }

    private isFromLessThanTo = (slot: Record<string, any>) => {
        const fromDate = new Date(`1970-01-01T${slot.from}:00`);
        const toDate = new Date(`1970-01-01T${slot.to}:00`);
        return fromDate < toDate;
    };

    private isTimeOverlapping(slot1: { from: string; to: string }, slot2: { from: String; to: String }): boolean {
        return slot1.from < slot2.to && slot1.to > slot2.from;
    }

    private isTimeWithinAvailableSlot(requestedSlot: { from: string; to: string }, availableSlot: { from: string; to: string }): boolean {
        // return requestedSlot.from >= availableSlot.from && requestedSlot.to <= availableSlot.to;
        return (
            new Date(`1970-01-01T${requestedSlot.from}:00`) >= new Date(`1970-01-01T${availableSlot.from}:00`) &&
            new Date(`1970-01-01T${requestedSlot.to}:00`) <= new Date(`1970-01-01T${availableSlot.to}:00`)
        );
    }

    // Common method to validate staff availability
    async checkStaffAvailability(
        serviceCategoryId: any,
        appointmentType: any,
        trainerId: string,
        date: Date,
        from: string,
        to: string,
        facilityId: string,
        scheduleId?: string,
    ): Promise<boolean> {
        // Fetch staff availability for the given date
        const staff = await this.UserModel.findOne({ _id: new Types.ObjectId(trainerId) }, { firstName: 1, lastName: 1 });
        if (!staff) throw new BadRequestException("Staff not found");
        const staffAvailabilities = await this.StaffAvailabilityModel.findOne({ userId: new Types.ObjectId(trainerId), date, facilityId: new Types.ObjectId(facilityId) });
        if (!staffAvailabilities) {
            throw new BadRequestException(`${staff.firstName} ${staff.lastName || ""} is not available for the selected services`);
        }

        // Get the available and unavailable time slots
        const availableSlots = staffAvailabilities.timeSlots.filter(
            (item) => item.availabilityStatus === StaffAvailabilityEnum.AVAILABLE && item.classType === ClassType.PERSONAL_APPOINTMENT,
        );
        const unavailableSlots = staffAvailabilities.timeSlots.filter((item) => item.availabilityStatus === StaffAvailabilityEnum.UNAVAILABLE);

        const reqFromDate = new Date(`1970-01-01T${from}:00`);
        const reqToDate = new Date(`1970-01-01T${to}:00`);

        // Check for conflicts with unavailable slots
        for (let slot of unavailableSlots) {
            const fromDate = new Date(`1970-01-01T${slot.from}:00`);
            const toDate = new Date(`1970-01-01T${slot.to}:00`);
            if (fromDate <= reqFromDate && toDate >= reqToDate) {
                throw new BadRequestException(`${staff.firstName} ${staff.lastName || ""} is not available at the given time`);
            }
        }

        // Check if trainer busy with another appointment
        const isTrainerBusy = await this.SchedulingModel.count({
            _id: { $ne: scheduleId },
            scheduleStatus: { $nin: [ScheduleStatusType.CANCELED] },
            trainerId: trainerId,
            date: date,
            $or: [{ from: { $gte: from, $lt: to } }, { to: { $gt: from, $lte: to } }],
        });
        if (isTrainerBusy) {
            throw new BadRequestException("Staff already occupied with another appointment");
        }

        // Collect all unique payRateIds from available slots
        const payRateIds = new Set<string>();
        for (let slot of availableSlots) {
            const fromDate = new Date(`1970-01-01T${slot.from}:00`);
            const toDate = new Date(`1970-01-01T${slot.to}:00`);
            if (fromDate <= reqFromDate && toDate >= reqToDate) {
                if (Array.isArray(slot.payRateIds)) {
                    slot.payRateIds.forEach((id: string) => payRateIds.add(id));
                }
            }
        }

        // If no payRateIds were found, staff is not available
        if (payRateIds.size === 0) {
            throw new BadRequestException(`${staff.firstName} ${staff.lastName || ""} is not available at the given time`);
        }

        // Query pay rates for all collected payRateIds
        const payRates = await this.PayRateModel.find({
            _id: { $in: Array.from(payRateIds) },
            serviceCategory: serviceCategoryId,
            appointmentType: appointmentType,
        });

        if (payRates.length > 0) {
            return true; // Staff is available
        }

        throw new BadRequestException(`${staff.firstName} ${staff.lastName || ""} is not available for the given sub type`);
    }

    // Common Method to handle session consumption
    private async consumeSessionUpdateInPurchase(purchaseId, sessions: number) {
        await this.PurchaseModel.updateOne(
            { _id: purchaseId },
            {
                $inc: {
                    sessionConsumed: sessions,
                },
            },
        );
        return true;
    }

    // Common Method to Validate Facility Availability
    private async validateFacilityAvailability(requestedDate: Date, requestedSlot: TimeSlotsDTO, facilitiesId: string) {
        const date = new Date(requestedDate);
        const dayOfWeek = date.toLocaleString("en-US", { weekday: "short" }).toLowerCase();
        const startOfDay = new Date(date.setHours(0, 0, 0, 0));
        const endOfDay = new Date(date.setHours(23, 59, 59, 999));
        const days = {
            mon: "Monday",
            tue: "Tuesday",
            wed: "Wednesday",
            thu: "Thursday",
            fri: "Friday",
            sat: "Saturday",
            sun: "Sunday",
        };
        const facilityRecords = await this.FacilityAvailabilityModel.find({
            facilityId: new Types.ObjectId(facilitiesId),
            $or: [
                {
                    type: "unavailable",
                    fromDate: { $lte: endOfDay },
                    endDate: { $gte: startOfDay },
                },
                {
                    type: "available",
                },
            ],
        });

        const unavailableRecord = facilityRecords.find((record) => record.type === "unavailable");
        const facilityAvailability = facilityRecords.find((record) => record.type === "available");

        if (unavailableRecord) {
            for (const unavailableSlot of unavailableRecord.time) {
                if (this.isTimeOverlapping(requestedSlot, unavailableSlot)) {
                    throw new BadRequestException(`Facility is unavailable from ${unavailableSlot.from} to ${unavailableSlot.to}`);
                }
            }
        }

        if (!facilityAvailability) {
            throw new BadRequestException("No available working hours found for the facility.");
        }

        const facilityWorkingHours = facilityAvailability.workingHours;

        const availableSlots = facilityWorkingHours[dayOfWeek];
        if (!availableSlots) {
            throw new BadRequestException(`Facility is not available on ${days[dayOfWeek.toLowerCase()]}.`);
        }

        const slotIsWithinAvailability = availableSlots.some((availableSlot: any) => this.isTimeWithinAvailableSlot(requestedSlot, availableSlot));

        if (!slotIsWithinAvailability) {
            const availableSlotsText = availableSlots.map((slot) => `${slot.from} to ${slot.to}`).join(", ");
            throw new BadRequestException(
                `Requested slot from ${requestedSlot.from} to ${requestedSlot.to} on ${days[dayOfWeek.toLowerCase()]
                } is outside facility's available hours. (Working hours for this day are: ${availableSlotsText}).`,
            );
        }
        return true;
    }

    // Common Method to Validate Package Eligibility
    private async validatePackageEligibility(
        clientId: string,
        date: Date,
        from: string,
        to: string,
        pricing: any,
        purchase: any,
        requestedSessions: number,
        scheduleId?: string,
        scheduledSession: number = 0,
    ) {
        if (!purchase || !pricing) {
            throw new BadRequestException("Access Denied as the user is not eligible for this service");
        }

        // Check if the purchase date range is valid for the requested date
        const packageStart = new Date(purchase.startDate);
        packageStart.setHours(0, 0, 0, 0); // Set to beginning of start date
        
        const packageEnd = new Date(purchase.endDate);
        packageEnd.setHours(23, 59, 59, 999); // Set to end of end date
        
        const bookingFromDateTime = new Date(`${date.toISOString().split("T")[0]}T${from}`);
        const bookingToDateTime = new Date(`${date.toISOString().split("T")[0]}T${to}`);

        if (bookingFromDateTime < packageStart || bookingToDateTime > packageEnd) {
            throw new BadRequestException(
                `This package is only valid till ${moment(packageEnd).format('Do MMM')}${moment(packageEnd).year() !== moment().year() ? ' ' + moment(packageEnd).format('YYYY') : ''}.`,
            );
        }

        if (bookingFromDateTime < packageStart || bookingFromDateTime > packageEnd) {
            throw new BadRequestException(
                `This package is only valid from ${moment(packageEnd).format('Do MMM')} till ${moment(packageEnd).format('Do MMM')}.`,
            );
        }

        // Check if the package is suspended
        const isSuspended =
            purchase.suspensions &&
            purchase.suspensions.find((suspension: Suspensions) => {
                return suspension.fromDate <= date && suspension.endDate >= date && !suspension.isResumed;
            });
        if (isSuspended) {
            throw new BadRequestException(`This membership is suspended from ${format(isSuspended.fromDate, "dd/MM/yyyy")} to ${format(isSuspended.endDate, "dd/MM/yyyy")}`);
        }

        // Check the consumed sessions
        const userConsumedSessions = purchase.sessionConsumed;

        let availableSessions: number = 0;

        switch (purchase.sessionType) {
            case SessionType.SINGLE:
                availableSessions = 1 - (userConsumedSessions - scheduledSession); // Only 1 session available for SINGLE session type
                break;
            case SessionType.ONCEADAY:
                const sessionsCounts = await this.SchedulingModel.aggregate([
                    {
                        $match: {
                            _id: { $ne: scheduleId },
                            purchaseId: purchase._id,
                            clientId: new Types.ObjectId(clientId),
                            date: date,
                            scheduleStatus: { $nin: [ScheduleStatusType.CANCELED] },
                        },
                    },
                    { $group: { _id: null, sessions: { $sum: "$sessions" } } },
                ]);
                const userSessionsForTheDay = sessionsCounts.length ? sessionsCounts[0]["sessions"] : 0;
                if (userSessionsForTheDay > 0 || requestedSessions > 1) {
                    throw new BadRequestException("Only one booking allowed per day with this package");
                }
                availableSessions = 1;
                break;
            case SessionType.MULTIPLE:
                availableSessions = purchase?.totalSessions - (userConsumedSessions - scheduledSession);
                break;
            case SessionType.DAY_PASS:
                const sessionsCount = await this.SchedulingModel.aggregate([
                    {
                        $match: {
                            _id: { $ne: scheduleId },
                            purchaseId: purchase._id,
                            clientId: new Types.ObjectId(clientId),
                            scheduleStatus: { $nin: [ScheduleStatusType.CANCELED] },
                        },
                    },
                    {
                        $group: {
                            _id: "$date",
                        },
                    },
                ]);
                const bookedDates = sessionsCount.map((item) => item._id.toISOString().split("T")[0]);
                const requestDate = new Date(date).toISOString().split("T")[0];
                const isDateAlreadyUsed = bookedDates.includes(requestDate);
                const consumedDayPassLimit = bookedDates.length;
                const assignedDayPassLimit = purchase?.dayPassLimit;
                const isEligibleForScheduling = assignedDayPassLimit - consumedDayPassLimit;

                if (isEligibleForScheduling > 0) {
                    availableSessions = Number.POSITIVE_INFINITY;
                } else if (isEligibleForScheduling <= 0) {
                    if (isDateAlreadyUsed) {
                        availableSessions = Number.POSITIVE_INFINITY;
                    } else {
                        throw new BadRequestException(
                            `Day pass limit reached ${assignedDayPassLimit} day(s). Please select an existing scheduled date to book a session.`,);
                    }
                }
                break;
            case SessionType.UNLIMITED:
                availableSessions = 0; // Unlimited sessions
                const sessionPerDay = purchase?.sessionPerDay || 0;
                if (sessionPerDay) {
                    const userSessionsForTheDay = await this.SchedulingModel.count({
                        _id: { $ne: scheduleId },
                        purchaseId: purchase._id,
                        clientId: new Types.ObjectId(clientId),
                        date: date,
                        scheduleStatus: { $nin: [ScheduleStatusType.CANCELED] },
                    });
                    // const userSessionsForTheDay = sessionsCount.length ? sessionsCount[0]['sessions'] : 0;
                    if (userSessionsForTheDay >= sessionPerDay) {
                        throw new BadRequestException(`Only ${sessionPerDay} booking allowed per day with this package`);
                    }
                    availableSessions = sessionPerDay - userSessionsForTheDay;
                }
                break;
            default:
                throw new BadRequestException("Invalid session type");
        }

        if (!availableSessions) {
            throw new BadRequestException(`Your session for the assigned package are exhausted`);
        }

        // Check if the requested sessions exceed the available sessions
        if (requestedSessions > availableSessions) {
            throw new BadRequestException(`Number of sessions requested is more than those available in this package`);
        }
    }

    private async validateFacilityOwnership(facility: any, user: IUserDocument) {
        const { role } = user
        let organizationId: any
        if (role.type == ENUM_ROLE_TYPE.ORGANIZATION) {
            organizationId = user._id
        }
        if (role.type == ENUM_ROLE_TYPE.WEB_MASTER || role.type == ENUM_ROLE_TYPE.FRONT_DESK_ADMIN || role.type == ENUM_ROLE_TYPE.TRAINER) {
            const staffDetails = await this.StaffProfileModel.findOne(
                { userId: user._id },
                { organizationId: 1 }
            );
            organizationId = staffDetails.organizationId
        }
        if (role.type == ENUM_ROLE_TYPE.USER) {
            const client = await this.ClientsModel.findOne({ userId: user.id }).exec();
            organizationId = client.organizationId;
        }

        if (!facility || facility.organizationId.toString() !== organizationId.toString()) {
            throw new BadRequestException("Access Denied as the Facility is not a part of this Organization");
        }
    }

    // Common Method to Validate Room Availability
    private async validateRoomAvailability(selectedRoom: any, date: Date, from: string, to: string, scheduledId?: any) {
        if (!selectedRoom) return true;
        // if (!selectedRoom) throw new BadRequestException(`Selected room is not available`);

        const currentRoomStrength = await this.SchedulingModel.count({
            _id: { $ne: scheduledId },
            roomId: selectedRoom._id,
            scheduleStatus: { $nin: [ScheduleStatusType.CANCELED] },
            date,
            from: { $lt: to },
            to: { $gt: from },
        });

        if (currentRoomStrength >= selectedRoom.capacity) {
            throw new BadRequestException(`Selected room is at full capacity at the selected time`);
        }
    }

    // Common Method to Check for Existing Booking Conflict
    /**
     * TODO:
     * - remove checkForExistingBookingConflict later
     */
    private async checkForExistingScheduleConflict(clientId: string, date: Date, from: string, to: string, scheduleId?: string) {
        const existingBooking = await this.SchedulingModel.find({
            _id: { $ne: scheduleId },
            classType: { $nin: [ClassType.BOOKINGS] },
            scheduleStatus: { $nin: [ScheduleStatusType.CANCELED] }, // skip canceled
            clientId,
            date,
        });

        for (let booking of existingBooking) {
            // Check for overlapping time slots
            const existingFrom = booking.from;
            const existingTo = booking?.to;

            if (
                existingBooking &&
                ((from >= existingFrom && from < existingTo) || // New start time falls within existing booking
                    (to > existingFrom && to <= existingTo) || // New end time falls within existing booking
                    (from <= existingFrom && to >= existingTo))
            ) {
                // New booking completely overlaps existing booking
                throw new BadRequestException(`Client has an overlapping appointment from ${existingFrom} to ${existingTo}`);
            }
        }
        return true;
    }

    private async checkForExistingSessionScheduleConflict(clientId: string, date: Date, from: string, to: string, scheduleId?: string) {
        const existingBooking = await this.SchedulingModel.find(
            {
                _id: { $ne: scheduleId },
                clientId: new Types.ObjectId(clientId),
                classType: { $in: [ClassType.BOOKINGS] },
                scheduleStatus: { $nin: [ScheduleStatusType.CANCELED] }, // skip canceled
                date,
            },
            { from: 1, to: 1 },
        );

        for (let booking of existingBooking) {
            // Check for overlapping time slots
            const existingFrom = booking.from;
            const existingTo = booking?.to;

            if (
                existingBooking &&
                ((from >= existingFrom && from < existingTo) || // New start time falls within existing booking
                    (to > existingFrom && to <= existingTo) || // New end time falls within existing booking
                    (from <= existingFrom && to >= existingTo))
            ) {
                // New booking completely overlaps existing booking
                throw new BadRequestException(`Client has an overlapping booking from ${existingFrom} to ${existingTo}`);
            }
        }
    }

    private async sendConfirmationEmail(dataForMail: any, isEditEmail: boolean) {
        if (dataForMail?.clientEmail) {
            dataForMail["date"] = moment(dataForMail.date).format("DD/MM/YYYY");
            await this.mailService.sendMail({
                to: dataForMail["clientEmail"].toString(),
                subject: isEditEmail
                    ? `${dataForMail.classType === "bookings" ? "Booking Edited Successfully" : "Appointment Edited Successfully"}`
                    : `${dataForMail.classType === "bookings" ? "Your Booking is Confirmed" : "Your Appointment is Confirmed"}`,
                template: "booking-creation",
                context: {
                    facilityName: dataForMail.facilityName || "Default Facility Name",
                    isEditEmail,
                    ...dataForMail,
                    dateOfBooking: moment().format("YYYY-MM-DD HH:mm:ss"),
                },
            });
        }

        if (dataForMail?.organizationEmail) {
            await this.mailService.sendMail({
                to: dataForMail["organizationEmail"].toString(),
                subject: isEditEmail
                    ? `${dataForMail.classType === "bookings" ? "Booking Edited Successfully" : "Appointment Edited Successfully"}`
                    : `${dataForMail.classType === "bookings" ? "New Booking Received" : "New Appointment Scheduled"}`,
                template: "organization-booking-creation", // Template for the organization email
                context: {
                    facilityName: dataForMail.facilityName || "Default Facility Name", // Add a fallback if undefined
                    isEditEmail,
                    ...dataForMail, // Spread other properties
                    dateOfBooking: moment().format("YYYY-MM-DD HH:mm:ss"),
                },
            });
        }
        if (dataForMail?.trainerEmail) {
            await this.mailService.sendMail({
                to: dataForMail["trainerEmail"].toString(),
                subject: isEditEmail
                    ? `${dataForMail.classType === "bookings" ? "Booking Edited Successfully" : "Appointment Edited Successfully"}`
                    : `${dataForMail.classType === "bookings" ? "New Booking Received" : "New Appointment Scheduled"}`,
                template: "organization-booking-creation", // Template for the organization email
                context: {
                    facilityName: dataForMail.facilityName || "Default Facility Name", // Add a fallback if undefined
                    isEditEmail,
                    ...dataForMail, // Spread other properties
                    dateOfBooking: moment().format("YYYY-MM-DD HH:mm:ss"),
                },
            });
        }
    }

    private async fetchNecessaryDocuments(body: CreateSchedulingDto | UpdateSchedulingDto, clientId: string, subType: string) {
        return await Promise.all([
            this.FacilityModel.findOne({ _id: new Types.ObjectId(body.facilityId) }),
            this.ServicesModel.findById(body.serviceCategory),
            // this.PayRteModel.findOne({_id: body.payRate}),
            // this.PricingModel.findById(body.packageId),
            this.PurchaseModel.findOne({ _id: body.purchaseId, isActive: true }),
            this.RoomModel.findOne({ _id: body.roomId, status: true }),
        ]);
    }

    async scheduleSession(body: CreateSchedulingDto, user: IUserDocument): Promise<any> {
        const { clientId, date, from, to, duration, sendConfirmation } = body;
        let roomId = body.roomId;
        const [checkFacility, servicePackage, purchase, selectedRoom] = await this.fetchNecessaryDocuments(body, clientId, body.subType);

        await this.validateFacilityOwnership(checkFacility, user);

        if (!purchase) {
            throw new BadRequestException("Access Denied as the user is not eligible for this service");
        }

        const pricing = await this.PricingModel.findById(purchase.packageId);
        if (!pricing) {
            throw new BadRequestException("Access Denied as the user is not eligible for this service");
        }

        if (!servicePackage) {
            throw new NotFoundException({ message: "Selected service not found" });
        }

        // Calculate Session Count
        let serviceSubType = servicePackage.appointmentType.find((item) => item._id.toString() === body.subType) as any;
        if (!serviceSubType) {
            const serviceIdFromPricing = pricing.services.serviceCategory;
            const subTypeIdsFromPricing = [...pricing.services.appointmentType];
            const serviceFromPricing = await this.ServicesModel.findOne({
                _id: serviceIdFromPricing,
                organizationId: body.organizationId,
                classType: body.classType,
                "appointmentType._id": { $in: subTypeIdsFromPricing },
            });
            serviceSubType = serviceFromPricing ? serviceFromPricing.appointmentType.find((item) => item._id.toString() === body.subType) : null;
        }
        if (!serviceSubType) {
            throw new BadRequestException({ message: `This package is not eligible for this ${body.classType} service` });
        }

        const appointmentDuration = serviceSubType.durationInMinutes;
        const requestedSessions = Math.floor(duration / appointmentDuration);

        await this.validatePackageEligibility(clientId, date, from, to, pricing, purchase, requestedSessions);
        if (user.role.type === ENUM_ROLE_TYPE.USER) {
            const rooms = await this.roomListForScheduling(body);
            if (!rooms.length) throw new BadRequestException(`No available rooms at the selected time`);
            roomId = rooms[0]?._id;
        } else await this.validateRoomAvailability(selectedRoom, date, from, to);
        await this.validateFacilityAvailability(date, { from, to }, body.facilityId);
        // await this.checkForExistingScheduleConflict(clientId, date, from, to);
        // await this.checkForExistingSessionScheduleConflict(clientId, date, from, to);

        // Create a New Booking
        const newSchedule = await new this.SchedulingModel({
            organizationId: body.organizationId,
            scheduledBy: user._id,
            facilityId: body.facilityId,
            subTypeId: body.subType,
            serviceCategoryId: servicePackage._id,
            packageId: purchase.packageId,
            purchaseId: purchase._id,
            clientId,
            classType: body.classType,
            roomId,
            dateRange: body.dateRange,
            duration,
            sessions: requestedSessions,
            date,
            from,
            to,
            scheduleStatus: body.checkIn ? ScheduleStatusType.CHECKEDIN : ScheduleStatusType.BOOKED,
            notes: body.notes,
        }).save();

        // update consumed sessions
        await this.consumeSessionUpdateInPurchase(purchase._id, requestedSessions);

        const schedule = await this.SchedulingModel.findById(newSchedule._id);

        // Send Confirmation Email if Requested
        const formattedSchedule = await this.formatSchedule(schedule);
        this.waitTimeGatewayService.sendWaitingTimeUpdate(body.facilityId);
        const mailData = await this.formatScheduleMailData(schedule);
        let isEditEmail = false;
        await this.sendConfirmationEmail(mailData, isEditEmail);
        return formattedSchedule;
    }


    async updateSession(body: UpdateSchedulingDto, user: IUserDocument): Promise<any> {
        const scheduledSession = await this.SchedulingModel.findById(body.scheduleId);
        if (!scheduledSession) throw new NotFoundException("Schedule not found");
        if (scheduledSession.scheduleStatus != ScheduleStatusType.BOOKED && scheduledSession.scheduleStatus != ScheduleStatusType.CHECKEDIN)
            throw new BadRequestException(`This session has already been ${scheduledSession.scheduleStatus} and cannot update`);

        const sessionStartDate = new Date(scheduledSession.date).setHours(0, 0, 0, 0);
        const currentDate = new Date().setHours(0, 0, 0, 0);
        const timeString = new Date().toLocaleTimeString("en-GB", { hour: "2-digit", minute: "2-digit" });
        if (currentDate > sessionStartDate || (currentDate == sessionStartDate && timeString > scheduledSession.to)) {
            throw new BadRequestException("You can only update it before or at the scheduled start time");
        }

        const { clientId: scheduledClient, purchaseId: scheduledPurchaseId } = scheduledSession;
        const clientId = body.clientId;
        const { facilityId, date, from, to, duration, sendConfirmation } = body;
        // const [checkFacility, payRate, pricing, purchase, selectedRoom] = await this.fetchNecessaryDocuments(body, clientId.toString(), body.subType);
        const [checkFacility, servicePackage, purchase, selectedRoom] = await this.fetchNecessaryDocuments(body, clientId, body.subType);

        await this.validateFacilityOwnership(checkFacility, user);

        if (!purchase) {
            throw new BadRequestException("Access Denied as the user is not eligible for this service");
        }

        const pricing = await this.PricingModel.findById(purchase.packageId);
        if (!pricing) {
            throw new BadRequestException("Access Denied as selected package not found");
        }

        if (!servicePackage) {
            throw new NotFoundException({ message: "Selected service not found" });
        }

        // Calculate Session Count
        let serviceSubType = servicePackage.appointmentType.find((item) => item._id.toString() === body.subType) as any;
        if (!serviceSubType) {
            const serviceIdFromPricing = pricing.services.serviceCategory;
            const subTypeIdsFromPricing = [...pricing.services.appointmentType];
            const serviceFromPricing = await this.ServicesModel.findOne({
                _id: serviceIdFromPricing,
                organizationId: scheduledSession.organizationId,
                classType: body.classType,
                "appointmentType._id": { $in: subTypeIdsFromPricing },
            });
            serviceSubType = serviceFromPricing ? serviceFromPricing.appointmentType.find((item) => item._id.toString() === body.subType) : null;
        }
        if (!serviceSubType) {
            throw new BadRequestException({ message: `This package is not eligible for this ${body.classType} service` });
        }

        const appointmentDuration = serviceSubType.durationInMinutes;
        const requestedSessions = Math.floor(duration / appointmentDuration);

        await this.validatePackageEligibility(clientId.toString(), date, from, to, pricing, purchase, requestedSessions, scheduledSession._id, scheduledSession.sessions);
        await this.validateRoomAvailability(selectedRoom, date, from, to, scheduledSession._id);
        await this.validateFacilityAvailability(date, { from, to }, facilityId);
        // await this.checkForExistingSessionScheduleConflict(clientId, date, from, to, scheduledSession._id);

        // update consumed sessions
        if (clientId != scheduledClient.toString()) {
            // Deduct in new package
            await this.consumeSessionUpdateInPurchase(purchase._id, requestedSessions);
            // Add session in older package
            await this.consumeSessionUpdateInPurchase(scheduledPurchaseId, -1 * scheduledSession.sessions);
        } else {
            const deductSession = requestedSessions - scheduledSession.sessions;
            await this.consumeSessionUpdateInPurchase(purchase._id, deductSession);
        }

        // Update the Schedule
        await this.SchedulingModel.updateOne(
            { _id: body.scheduleId },
            {
                $set: {
                    scheduledBy: user._id,
                    facilityId: body.facilityId,
                    subTypeId: body.subType,
                    serviceCategoryId: servicePackage._id,
                    packageId: purchase.packageId,
                    purchaseId: purchase._id,
                    clientId,
                    classType: body.classType,
                    roomId: body.roomId,
                    dateRange: body.dateRange,
                    duration,
                    sessions: requestedSessions,
                    date,
                    from,
                    to,
                    notes: body.notes || scheduledSession.notes,
                },
            },
        );

        const updatedSchedule = await this.SchedulingModel.findById(body.scheduleId);

        let formatSchedule = await this.formatSchedule(updatedSchedule);
        this.waitTimeGatewayService.sendWaitingTimeUpdate(body.facilityId);
        const mailData = await this.formatScheduleMailData(updatedSchedule);
        let isEditEmail = true;
        await this.sendConfirmationEmail(mailData, isEditEmail);
        return formatSchedule;
    }

    private async roomListForScheduling(body: any): Promise<any> {
        const { facilityId, classType, serviceCategory, date, from, to } = body;
        let startDate = new Date(date);
        const startOfDay = new Date(startDate.setHours(0, 0, 0, 0));
        const endOfDay = new Date(startDate.setHours(23, 59, 59, 999));
        if (from > to) {
            throw new BadRequestException("StartTime must be greater than end time");
        }
        try {
            const bookedRoomsPipeline = [
                {
                    $match: {
                        facilityId: new Types.ObjectId(facilityId),
                        classType,
                        serviceCategory: new Types.ObjectId(serviceCategory),
                        status: true,
                    },
                },
                {
                    $lookup: {
                        from: "schedulings",
                        localField: "_id",
                        foreignField: "roomId",
                        as: "scheduleDetails",
                    },
                },
                { $unwind: { path: "$scheduleDetails", preserveNullAndEmptyArrays: true } },
                {
                    $match: {
                        "scheduleDetails.facilityId": new Types.ObjectId(facilityId),
                        "scheduleDetails.scheduleStatus": {
                            $in: [ScheduleStatusType.BOOKED, ScheduleStatusType.CHECKEDIN],
                        },
                        "scheduleDetails.date": { $gte: startOfDay, $lte: endOfDay },
                        "scheduleDetails.from": { $lt: to },
                        "scheduleDetails.to": { $gt: from },
                    },
                },
                {
                    $group: {
                        _id: "$_id",
                        roomName: { $first: "$roomName" },
                        capacity: { $first: "$capacity" },
                        bookings: { $push: "$scheduleDetails" },
                    },
                },
                {
                    $addFields: {
                        bookingsCount: { $size: "$bookings" },
                    },
                },
                {
                    $project: {
                        _id: 1,
                        roomId: 1,
                        roomName: 1,
                        capacity: 1,
                        bookingsCount: 1,
                    },
                },
            ];
            const bookedRooms = await this.RoomModel.aggregate(bookedRoomsPipeline);

            const roomIds = [];
            const bookedRoomsMap = new Map();

            for (const room of bookedRooms) {
                if (room.bookingsCount >= room?.capacity) {
                    roomIds.push(new Types.ObjectId(room._id));
                }
                bookedRoomsMap.set(room._id.toString(), room.bookingsCount);
            }

            const availableRoomsPipeline = [
                {
                    $match: {
                        facilityId: new Types.ObjectId(facilityId),
                        _id: { $nin: roomIds },
                        classType: { $in: [classType] },
                        serviceCategory: { $in: [new Types.ObjectId(serviceCategory)] },
                        status: true,
                    },
                },
                {
                    $project: {
                        _id: 1,
                        roomId: "$_id",
                        roomName: 1,
                        capacity: 1,
                    },
                },
            ];

            const availableRooms = await this.RoomModel.aggregate(availableRoomsPipeline);

            const finalResult = availableRooms
                .map((room) => ({
                    ...room,
                    bookingsCount: bookedRoomsMap.get(room._id.toString()) || 0,
                }))
                .sort((a, b) => a._id.toString().localeCompare(b._id.toString()));

            return finalResult;
        } catch (error) {
            console.error("Error in roomList aggregation:", error.message);
            throw new BadRequestException("Failed to fetch rooms");
        }
    }

    async schedulePersonalAppointment(body: CreateSchedulingDto, user: IUserDocument) {
        const { clientId, date, from, to, duration, sendConfirmation } = body;
        let roomId = body.roomId;
        const [checkFacility, servicePackage, purchase, selectedRoom] = await this.fetchNecessaryDocuments(body, clientId, body.subType);

        if (!purchase) {
            throw new BadRequestException("Access Denied as the user is not eligible for this service");
        }

        const pricing = await this.PricingModel.findById(purchase.packageId);
        if (!pricing) {
            throw new BadRequestException("Access Denied as the user is not eligible for this service");
        }

        if (!servicePackage) {
            throw new NotFoundException({ message: "Selected service not found" });
        }

        // Calculate Session Count
        let serviceSubType = servicePackage.appointmentType.find((item) => item._id.toString() === body.subType) as any;
        if (!serviceSubType) {
            const serviceIdFromPricing = pricing.services.serviceCategory;
            const subTypeIdsFromPricing = [...pricing.services.appointmentType];
            const serviceFromPricing = await this.ServicesModel.findOne({
                _id: serviceIdFromPricing,
                organizationId: body.organizationId,
                classType: body.classType,
                "appointmentType._id": { $in: subTypeIdsFromPricing },
            });
            serviceSubType = serviceFromPricing ? serviceFromPricing.appointmentType.find((item) => item._id.toString() === body.subType) : null;
        }
        if (!serviceSubType) {
            throw new BadRequestException({ message: `This package is not eligible for this ${body.classType} service` });
        }

        const appointmentDuration = serviceSubType.durationInMinutes;
        const requestedSessions = Math.floor(duration / appointmentDuration);

        await this.validateFacilityOwnership(checkFacility, user);
        await this.validatePackageEligibility(clientId, date, from, to, pricing, purchase, requestedSessions);
        if (user.role.type === ENUM_ROLE_TYPE.USER) {
            const rooms = await this.roomListForScheduling(body);
            if (!rooms.length) throw new BadRequestException(`No available rooms at the selected time`);
            roomId = rooms[0]?._id;
        } else await this.validateRoomAvailability(selectedRoom, date, from, to);
        await this.validateFacilityAvailability(date, { from, to }, body.facilityId);
        await this.checkForExistingScheduleConflict(clientId, date, from, to);

        // validate staff availability by time slot
        const staff = await this.StaffProfileModel.findOne({ userId: body.trainerId, facilityId: body.facilityId });
        if (!staff) {
            throw new BadRequestException("Staff is not part of this facility");
        }
        await this.checkStaffAvailability(body.serviceCategory, body.subType, body.trainerId, body.date, from, to, body.facilityId);

        // Create a New Booking
        const newSchedule = await new this.SchedulingModel({
            organizationId: body.organizationId,
            scheduledBy: user._id,
            facilityId: body.facilityId,
            trainerId: staff.userId,
            subTypeId: body.subType,
            serviceCategoryId: servicePackage._id,
            packageId: purchase.packageId,
            purchaseId: purchase._id,
            clientId,
            classType: body.classType,
            roomId,
            dateRange: body.dateRange,
            duration,
            sessions: requestedSessions,
            date,
            from,
            to,
            scheduleStatus: body.checkIn ? ScheduleStatusType.CHECKEDIN : ScheduleStatusType.BOOKED,
            notes: body.notes,
        }).save();

        // update consumed sessions
        await this.consumeSessionUpdateInPurchase(purchase._id, requestedSessions);

        const schedule = await this.SchedulingModel.findById(newSchedule._id);

        const formattedSchedule = await this.formatSchedule(schedule);
        this.waitTimeGatewayService.sendWaitingTimeUpdate(body.facilityId);
        const mailData = await this.formatScheduleMailData(schedule);
        let isEditEmail = false;
        await this.sendConfirmationEmail(mailData, isEditEmail);

        return formattedSchedule;
    }

    async updatePersonalAppointment(body: UpdateSchedulingDto, user: IUserDocument): Promise<any> {
        const role = user.role;
        const scheduledAppointment = await this.SchedulingModel.findById(body.scheduleId);
        if (!scheduledAppointment) throw new NotFoundException("Schedule not found");
        if (scheduledAppointment.scheduleStatus != ScheduleStatusType.BOOKED && scheduledAppointment.scheduleStatus != ScheduleStatusType.CHECKEDIN)
            throw new BadRequestException(`This session has already been ${scheduledAppointment.scheduleStatus} and cannot update`);
        if (role.type === ENUM_ROLE_TYPE.TRAINER && (!scheduledAppointment.trainerId || scheduledAppointment.trainerId.toString() !== user._id.toString())) {
            throw new BadRequestException("Access denied - Trainer can only update their own scheduled appointments");
        }

        const sessionStartDate = new Date(scheduledAppointment.date).setHours(0, 0, 0, 0);
        const currentDate = new Date().setHours(0, 0, 0, 0);
        const timeString = new Date().toLocaleTimeString("en-GB", { hour: "2-digit", minute: "2-digit" });
        if (currentDate > sessionStartDate || (currentDate == sessionStartDate && timeString > scheduledAppointment.to)) {
            throw new BadRequestException("You can only update it before or at the scheduled start time");
        }

        const { clientId: scheduledClient, purchaseId: scheduledPurchaseId } = scheduledAppointment;
        const clientId = body.clientId;
        const { facilityId, date, from, to, duration, sendConfirmation } = body;
        // const [checkFacility, payRate, pricing, purchase, selectedRoom] = await this.fetchNecessaryDocuments(body, clientId.toString(), body.subType);
        const [checkFacility, servicePackage, purchase, selectedRoom] = await this.fetchNecessaryDocuments(body, clientId, body.subType);

        if (!purchase) {
            throw new BadRequestException("Access Denied as the user is not eligible for this service");
        }

        const pricing = await this.PricingModel.findById(purchase.packageId);
        if (!pricing) {
            throw new BadRequestException("Access Denied as the user is not eligible for this service");
        }

        if (!servicePackage) {
            throw new NotFoundException({ message: "Selected service not found" });
        }

        // Calculate Session Count
        let serviceSubType = servicePackage.appointmentType.find((item) => item._id.toString() === body.subType) as any;
        if (!serviceSubType) {
            const serviceIdFromPricing = pricing.services.serviceCategory;
            const subTypeIdsFromPricing = [...pricing.services.appointmentType];
            const serviceFromPricing = await this.ServicesModel.findOne({
                _id: serviceIdFromPricing,
                organizationId: scheduledAppointment.organizationId,
                classType: body.classType,
                "appointmentType._id": { $in: subTypeIdsFromPricing },
            });
            serviceSubType = serviceFromPricing ? serviceFromPricing.appointmentType.find((item) => item._id.toString() === body.subType) : null;
        }
        if (!serviceSubType) {
            throw new BadRequestException({ message: `This package is not eligible for this ${body.classType} service` });
        }

        const appointmentDuration = serviceSubType.durationInMinutes;
        const requestedSessions = Math.floor(duration / appointmentDuration);
        const updatedSessions = requestedSessions - scheduledAppointment.sessions;

        await this.validateFacilityOwnership(checkFacility, user);
        await this.validatePackageEligibility(clientId.toString(), date, from, to, pricing, purchase, updatedSessions, scheduledAppointment._id, scheduledAppointment.sessions);
        await this.validateRoomAvailability(selectedRoom, date, from, to, scheduledAppointment._id);
        await this.validateFacilityAvailability(date, { from, to }, facilityId);
        await this.checkForExistingScheduleConflict(clientId, date, from, to, scheduledAppointment._id);

        // validate staff availability by time slot
        const staff = await this.StaffProfileModel.findOne({ userId: body.trainerId, facilityId: body.facilityId });
        if (!staff) {
            throw new BadRequestException("Staff is not part of this facility");
        }

        await this.checkStaffAvailability(body.serviceCategory, body.subType, body.trainerId, body.date, from, to, body.facilityId, scheduledAppointment._id);

        // update consumed sessions
        if (clientId != scheduledClient.toString()) {
            // Deduct in new package
            await this.consumeSessionUpdateInPurchase(purchase._id, requestedSessions);
            // Add session in older package
            await this.consumeSessionUpdateInPurchase(scheduledPurchaseId, -1 * scheduledAppointment.sessions);
        } else {
            const deductSession = requestedSessions - scheduledAppointment.sessions;
            await this.consumeSessionUpdateInPurchase(purchase._id, deductSession);
        }

        // Update the Schedule
        await this.SchedulingModel.updateOne(
            { _id: body.scheduleId },
            {
                $set: {
                    scheduledBy: user._id,
                    facilityId: body.facilityId,
                    trainerId: staff.userId,
                    subTypeId: body.subType,
                    serviceCategoryId: servicePackage._id,
                    packageId: purchase.packageId,
                    purchaseId: purchase._id,
                    clientId,
                    classType: body.classType,
                    roomId: body.roomId,
                    dateRange: body.dateRange,
                    duration,
                    sessions: requestedSessions,
                    date,
                    from,
                    to,
                    notes: body.notes || scheduledAppointment.notes,
                },
            },
        );

        const updatedSchedule = await this.SchedulingModel.findById(body.scheduleId);

        // Send Confirmation Email if Requested

        let formatedSchedule = await this.formatSchedule(updatedSchedule);
        this.waitTimeGatewayService.sendWaitingTimeUpdate(body.facilityId);
        const mailData = await this.formatScheduleMailData(updatedSchedule);
        let isEditEmail = true;
        await this.sendConfirmationEmail(mailData, isEditEmail);

        return formatedSchedule;
    }

    async getScheduleDetails(user: Record<string, any>, scheduleId: string): Promise<Record<string, any>> {
        const { role } = user;
        const query = { _id: new Types.ObjectId(scheduleId) };

        switch (role.type) {
            case ENUM_ROLE_TYPE.USER:
                query['clientId'] = user._id
                break
            case ENUM_ROLE_TYPE.ORGANIZATION:
                query['organizationId'] = user._id
                break
            case ENUM_ROLE_TYPE.WEB_MASTER:
            case ENUM_ROLE_TYPE.FRONT_DESK_ADMIN:
                const staffDetails = await this.StaffProfileModel.findOne(
                    { userId: user._id },
                    { organizationId: 1 }
                );
                if (!staffDetails) throw new BadRequestException("Access denied, Staff does not have access");
                query['organizationId'] = staffDetails.organizationId
                break
            case ENUM_ROLE_TYPE.TRAINER:
                const trainerDetails = await this.StaffProfileModel.findOne(
                    { userId: user._id },

                    { organizationId: 1 },
                );

                if (!trainerDetails) throw new BadRequestException("Access denied, Staff does not have access");

                query["organizationId"] = trainerDetails.organizationId;

                query["trainerId"] = user._id;
                break;
            default:
                throw new BadRequestException("Access denied");
        }
        let schedule: any = await this.SchedulingModel.findOne(query);
        if (!schedule) {
            throw new NotFoundException("Schedule not found");
        }

        return await this.formatSchedule(schedule);
    }

    async formatSchedule(schedule) {
        const [service, client] = await Promise.all([
            this.ServicesModel.findOne({ _id: schedule.serviceCategoryId }, { appointmentType: { $elemMatch: { _id: schedule.subTypeId } } }),
            this.ClientsModel.findOne({ userId: schedule.clientId }, { userId: 1, clientId: 1 }),
        ]);

        const usedSubType: any = service?.appointmentType?.[0] || {};
        const populatedSchedule = await schedule.populate([
            { path: "scheduledBy", select: "_id name firstName lastName email" },
            { path: "facilityId", select: "_id facilityName address profilePicture billingDetails" },
            { path: "trainerId", select: "_id name firstName lastName email" },
            { path: "organizationId", select: "_id name email" },
            { path: "clientId", select: "_id firstName lastName email mobile isActive" },
            { path: "packageId", select: "_id name price services expiredInDays durationUnit" },
            { path: "purchaseId", select: "_id sessionConsumed totalSessions sharePass" },
            // { path: "subTypeId", select: "_id name description classType" },
            { path: "serviceCategoryId", select: "_id name classType appointmentType isActive" },
            { path: "roomId", select: "_id roomName capacity description" },
        ]);

        const { packageId, purchaseId } = populatedSchedule;
        const sessionType = packageId?.services?.sessionType;

        const remainingSessions =
            sessionType === SessionType.SINGLE
                ? 0
                : sessionType === SessionType.MULTIPLE
                    ? (purchaseId?.sharePass ? purchaseId.totalSessions : packageId.services.sessionCount) - purchaseId.sessionConsumed
                    : 9999;

        const schedulearray = [schedule]
        const enrichedSchedules = schedulearray.map((schedule: any) => {
            const appointmentTypes = schedule?.serviceCategoryId?.appointmentType || [];
            const matchedAppointment = appointmentTypes.find((apt: any) => apt._id.toString() === schedule.subTypeId?.toString());

            return {
                ...schedule.toObject(),
                subTypeName: matchedAppointment?.name || null,
                subTypeId: matchedAppointment?._id || null,
            };
        });
        return {
            _id: populatedSchedule?._id,
            scheduledBy: populatedSchedule?.scheduledBy?._id,
            facilityId: populatedSchedule?.facilityId?._id,
            organizationId: populatedSchedule?.organizationId?._id,
            clientId: populatedSchedule?.clientId?._id,
            clientName: `${populatedSchedule?.clientId?.firstName ?? ""} ${populatedSchedule?.clientId?.lastName ?? ""}`.trim(),
            clientFirstName: populatedSchedule?.clientId?.firstName ?? "",
            clientLastName: populatedSchedule?.clientId?.lastName ?? "",
            clientEmail: populatedSchedule?.clientId?.email ?? "",
            clientPhone: populatedSchedule?.clientId?.mobile ?? "",
            customerId: client?.clientId ?? "",
            trainerId: populatedSchedule?.trainerId?._id,
            trainerName: populatedSchedule?.trainerId ? `${populatedSchedule?.trainerId?.firstName ?? ""} ${populatedSchedule?.trainerId?.lastName ?? ""}`.trim() : "",
            purchaseId: purchaseId?._id ?? "",
            packageId: packageId?._id ?? "",
            classType: populatedSchedule?.classType ?? "",
            subTypeId: populatedSchedule?.subTypeId?._id ?? "",
            subTypeList: populatedSchedule?.serviceCategoryId?.appointmentType ?? [],
            serviceCategoryId: populatedSchedule?.serviceCategoryId?._id ?? "",
            roomId: populatedSchedule?.roomId?._id ?? null,
            roomName: populatedSchedule?.roomId?.roomName ?? "",
            remainingSessions: remainingSessions ?? 0,
            subTypeDuration: usedSubType?.durationInMinutes ?? 0,
            duration: populatedSchedule?.duration ?? 0,
            sessions: populatedSchedule?.sessions ?? 0,
            date: populatedSchedule?.date ?? null,
            from: populatedSchedule?.from ?? null,
            to: populatedSchedule?.to ?? null,
            scheduleStatus: populatedSchedule?.scheduleStatus ?? "",
            notes: populatedSchedule?.notes ?? "",
            facilityName: populatedSchedule?.facilityId?.facilityName ?? "",
            packageName: packageId?.name ?? "",
            totalSession: purchaseId?.sharePass ? purchaseId?.totalSessions ?? 0 : packageId?.services?.sessionCount ?? 0,
            sessionConsumed: purchaseId?.sessionConsumed ?? 0
        };
    }

    async formatScheduleMailData(schedule) {
        const [service, client, facility]: any = await Promise.all([
            this.ServicesModel.findOne({ _id: schedule.serviceCategoryId }, { appointmentType: { $elemMatch: { _id: schedule.subTypeId } } }),
            this.ClientsModel.findOne({ userId: schedule.clientId }, { userId: 1, clientId: 1 }),
            this.FacilityModel.findById(schedule.facilityId, { billingDetails: 1, _id: 1 }).populate([
                { path: "billingDetails.state", select: "_id name" },
                { path: "billingDetails.city", select: "_id name" },
            ]),
        ]);

        const usedSubType: any = service?.appointmentType?.[0] || {};
        const populatedSchedule = await schedule.populate([
            { path: "scheduledBy", select: "_id name firstName lastName email" },
            { path: "facilityId", select: "_id facilityName address profilePicture billingDetails" },
            { path: "trainerId", select: "_id name firstName lastName email" },
            { path: "organizationId", select: "_id name email" },
            { path: "clientId", select: "_id firstName lastName email mobile isActive" },
            { path: "packageId", select: "_id name price services expiredInDays durationUnit" },
            { path: "purchaseId", select: "_id sessionConsumed totalSessions sharePass" },
            { path: "subTypeId", select: "_id name description classType" },
            { path: "serviceCategoryId", select: "_id name classType appointmentType isActive" },
            { path: "roomId", select: "_id roomName capacity description" },
        ]);

        const { packageId, purchaseId } = populatedSchedule;
        const sessionType = packageId?.services?.sessionType;

        const remainingSessions =
            sessionType === SessionType.SINGLE
                ? 0
                : sessionType === SessionType.MULTIPLE
                    ? (purchaseId?.sharePass ? purchaseId.totalSessions : packageId.services.sessionCount) - purchaseId.sessionConsumed
                    : 9999;

        const billingDetails = !(facility && facility.billingDetails)
            ? null
            : {
                _id: facility.billingDetails._id,
                billingName: facility.billingDetails.billingName,
                addressLine1: facility.billingDetails.addressLine1,
                addressLine2: facility.billingDetails.addressLine2 ? facility.billingDetails.addressLine2 : "",
                state: facility.billingDetails.state.name,
                city: facility.billingDetails.city.name,
                postalCode: facility.billingDetails.postalCode,
                gstNumber: facility.billingDetails.gstNumber,
            };

        return {
            _id: populatedSchedule._id,
            scheduledBy: populatedSchedule.scheduledBy?._id,
            facilityId: populatedSchedule.facilityId?._id,
            billingDetails,
            organizationId: populatedSchedule.organizationId?._id,
            organizationEmail: populatedSchedule.organizationId.email,
            clientId: populatedSchedule.clientId?._id,
            clientName: `${populatedSchedule.clientId?.firstName} ${populatedSchedule.clientId?.lastName}`,
            clientFirstName: populatedSchedule.clientId?.firstName,
            clientLastName: populatedSchedule.clientId?.lastName,
            clientEmail: populatedSchedule.clientId?.email,
            clientPhone: populatedSchedule.clientId?.mobile,
            customerId: client?.clientId,
            trainerId: populatedSchedule.trainerId?._id || "",
            trainerName: populatedSchedule.trainerId ? `${populatedSchedule.trainerId.firstName} ${populatedSchedule.trainerId.lastName}` : "",
            trainerEmail: populatedSchedule.trainerId ? populatedSchedule.trainerId.email : "",
            purchaseId: purchaseId?._id,
            packageId: packageId?._id,
            classType: populatedSchedule.classType,
            subTypeId: populatedSchedule.subTypeId?._id,
            subTypeList: populatedSchedule.serviceCategoryId?.appointmentType || [],
            serviceCategoryId: populatedSchedule.serviceCategoryId?._id,
            roomId: populatedSchedule.roomId?._id || null,
            roomName: populatedSchedule.roomId?.roomName || "",
            remainingSessions,
            subTypeDuration: usedSubType?.durationInMinutes,
            duration: populatedSchedule.duration,
            sessions: populatedSchedule.sessions,
            date: populatedSchedule.date,
            from: populatedSchedule.from,
            to: populatedSchedule.to,
            scheduleStatus: populatedSchedule.scheduleStatus,
            notes: populatedSchedule.notes,
            facilityName: populatedSchedule.facilityId?.facilityName,
            packageName: packageId?.name,
            totalSession: purchaseId?.sharePass ? purchaseId.totalSessions : packageId?.services?.sessionCount,
            sessionConsumed: purchaseId?.sessionConsumed || 0,
        };
    }

    async getSchedulesList(
        user: IUserDocument,
        body: GetSchedulesDto
    ): Promise<any> {
        const { search, facilityId, classType, serviceCategory, trainerId, roomId, startDate, endDate, page, pageSize } = body;
        const skip = pageSize * (page - 1);
        const filter: any = { scheduleStatus: { $in: [ScheduleStatusType.BOOKED, ScheduleStatusType.CHECKEDIN] } };

        const { role } = user;
        switch (role.type) {
            case ENUM_ROLE_TYPE.USER:
                filter['clientId'] = user._id
                break
            case ENUM_ROLE_TYPE.ORGANIZATION:
                filter['organizationId'] = user._id
                break
            case ENUM_ROLE_TYPE.WEB_MASTER:
            case ENUM_ROLE_TYPE.FRONT_DESK_ADMIN:
                const staffDetails = await this.StaffProfileModel.findOne(
                    { userId: user._id },
                    { organizationId: 1 }
                );
                if (!staffDetails) throw new BadRequestException("Access denied, Staff does not have access");
                filter['organizationId'] = staffDetails.organizationId
                break
            case ENUM_ROLE_TYPE.TRAINER:
                const trainerDetails = await this.StaffProfileModel.findOne(
                    { userId: user._id },
                    { organizationId: 1 }
                );
                if (!trainerDetails) throw new BadRequestException("Access denied, Staff does not have access");
                filter["organizationId"] = trainerDetails.organizationId;
                filter["trainerId"] = user._id;
                break;
            default:
                throw new BadRequestException("Access denied");
        }

        if (body.scheduleStatus) {
            filter["scheduleStatus"] = body.scheduleStatus;
        }
        if (facilityId.length) {
            filter["facilityId"] = { $in: facilityId.map((_id) => new Types.ObjectId(_id)) };
        }
        if (classType) {
            filter["classType"] = classType;
        }
        if (serviceCategory.length) {
            filter["serviceCategoryId"] = { $in: serviceCategory.map((_id) => new Types.ObjectId(_id)) };
        }
        if (roomId.length) {
            filter["roomId"] = { $in: roomId.map((_id) => new Types.ObjectId(_id)) };
        }
        if (trainerId.length) {
            filter["trainerId"] = { $in: trainerId.map((_id) => new Types.ObjectId(_id)) };
        }
        if (startDate && endDate) {
            filter["date"] = {
                $gte: startDate,
                $lte: endDate,
            };
        }
        const agg: PipelineStage[] = [
            {
                $match: filter,
            },
            {
                $lookup: {
                    from: "users",
                    localField: "clientId",
                    foreignField: "_id",
                    as: "client",
                    pipeline: [
                        {
                            $project: {
                                name: 1,
                                firstNam: 1,
                                lastName: 1,
                                email: 1,
                                mobile: 1,
                                isActive: 1,
                            },
                        },
                    ],
                },
            },
            {
                $set: {
                    client: {
                        $arrayElemAt: ["$client", 0],
                    },
                },
            },
            ...(search
                ? [
                    {
                        $addFields: {
                            searched: { $regexMatch: { input: "$client.name", regex: search, options: "i" } },
                        },
                    },
                    {
                        $match: {
                            searched: true,
                        },
                    },
                ]
                : []),
            {
                $facet: {
                    total: [
                        {
                            $count: "count",
                        },
                    ],
                    scheduleIds: [
                        {
                            $sort: {
                                updatedAt: -1,
                            },
                        },
                        // {
                        //     $skip: skip,
                        // },
                        // {
                        //     $limit: pageSize,
                        // },
                        {
                            $project: {
                                _id: 1,
                            },
                        },
                    ],
                },
            },
            {
                $replaceRoot: {
                    newRoot: {
                        total: {
                            $arrayElemAt: ["$total.count", 0],
                        },
                        scheduleIds: "$scheduleIds",
                    },
                },
            },
        ];
        const idsList = await this.SchedulingModel.aggregate(agg);
        const { total = 0, scheduleIds } = idsList.length ? idsList[0] : { total: 0, scheduleIds: [] };
        const populateFields = [
            { path: "scheduledBy", select: "_id name firstName lastName email" },
            { path: "facilityId", select: "_id facilityName address profilePicture" },
            { path: "trainerId", select: "_id name firstName lastName email" },
            // { path: 'organizationId', select: '_id name email' },
            { path: "clientId", select: "_id firstName lastName email mobile isActive" },
            { path: "packageId", select: "_id name price expiredInDays durationUnit" },
            // { path: 'subTypeId', select: '_id name description classType' },
            { path: "serviceCategoryId", select: "_id name classType appointmentType isActive" },
            { path: "roomId", select: "_id roomName capacity description" },
        ];
        const schedules = await this.SchedulingModel.find({ _id: { $in: scheduleIds.map((item) => item._id) } })
            .sort({ updatedAt: -1 })
            .populate(populateFields);
        const enrichedSchedules = schedules.map((schedule: any) => {
            const appointmentTypes = schedule?.serviceCategoryId?.appointmentType || [];
            const matchedAppointment = appointmentTypes.find((apt: any) => apt._id.toString() === schedule.subTypeId?.toString());

            return {
                ...schedule.toObject(),
                subTypeName: matchedAppointment?.name || null,
                subTypeId: matchedAppointment?._id || null,
            };
        });
        const userIds = enrichedSchedules?.filter((item: any) => item.clientId?._id).map((item: any) => item.clientId?._id);
        const clients = await this.ClientsModel.find({ userId: { $in: userIds } }, { userId: 1, clientId: 1 });
        // const customerId = await clients.find(client=>(client.userId == item.clientId._id)),
        const data = enrichedSchedules?.map((item: any) => {
            let customerId: any = clients.find((client) => client.userId.toString() == item.clientId?._id?.toString());
            customerId = customerId ? customerId.clientId : "";
            return {
                _id: item?._id,
                facilityId: item?.facilityId?._id,
                trainerId: item?.trainerId?._id ?? "",
                trainerName: item?.trainerId ? `${item?.trainerId?.firstName ?? ""} ${item?.trainerId?.lastName ?? ""}`.trim() : "",
                facilityName: item?.facilityId?.facilityName ?? "",
                clientId: item?.clientId?._id,
                clientName: item?.clientId ? `${item?.clientId?.firstName ?? ""} ${item?.clientId?.lastName ?? ""}`.trim() : null,
                clientFirstName: item?.clientId?.firstName ?? "",
                clientLastName: item?.clientId?.lastName ?? "",
                clientEmail: item?.clientId?.email ?? "",
                clientPhone: item?.clientId?.mobile ?? "",
                customerId: customerId ?? "",
                packageId: item?.packageId?._id,
                packageName: item?.packageId?.name ?? "",
                classType: item?.classType ?? "",
                scheduleStatus: item?.scheduleStatus ?? "",
                serviceCategoryId: item?.serviceCategoryId?._id,
                serviceCategoryName: item?.serviceCategoryId?.name ?? "",
                subTypeId: item?.subTypeId?._id,
                subTypeName: item?.subTypeName ?? "",
                duration: item?.duration ?? 0,
                sessions: item?.sessions ?? 0,
                room: item?.roomId?._id ?? null,
                roomName: item?.roomId?.roomName ?? "",
                date: item?.date ?? null,
                from: item?.from ?? null,
                to: item?.to ?? null
            };
        });

        return {
            count: total,
            data,
        };
    }

    async getSchedulesListV1(user: Record<string, any>, body: GetSchedulesDto): Promise<any> {
        const { search, facilityId, classType, serviceCategory, trainerId, roomId, startDate, endDate, page, pageSize } = body;
        const skip = pageSize * (page - 1);
        const filter: any = { scheduleStatus: { $in: [ScheduleStatusType.BOOKED, ScheduleStatusType.CHECKEDIN] } };

        const { role } = user;
        switch (role.type) {
            case ENUM_ROLE_TYPE.USER:
                filter["clientId"] = user._id;
                break;
            case ENUM_ROLE_TYPE.ORGANIZATION:
                filter["organizationId"] = user._id;
                break;
            case ENUM_ROLE_TYPE.WEB_MASTER:
            case ENUM_ROLE_TYPE.FRONT_DESK_ADMIN:
                const staffDetails = await this.StaffProfileModel.findOne({ userId: user._id }, { organizationId: 1 });
                if (!staffDetails) throw new BadRequestException("Access denied, Staff does not have access");
                filter["organizationId"] = staffDetails.organizationId;
                break;
            case ENUM_ROLE_TYPE.TRAINER:
                const trainerDetails = await this.StaffProfileModel.findOne({ userId: user._id }, { organizationId: 1 });
                if (!trainerDetails) throw new BadRequestException("Access denied, Staff does not have access");
                filter["organizationId"] = trainerDetails.organizationId;
                filter["trainerId"] = user._id;
                break;
            default:
                throw new BadRequestException("Access denied");
        }

        if (body.scheduleStatus) {
            filter["scheduleStatus"] = body.scheduleStatus;
        }
        if (facilityId.length) {
            filter["facilityId"] = { $in: facilityId.map((_id) => new Types.ObjectId(_id)) };
        }
        if (classType) {
            filter["classType"] = classType;
        }
        if (serviceCategory.length) {
            filter["serviceCategoryId"] = { $in: serviceCategory.map((_id) => new Types.ObjectId(_id)) };
        }
        if (roomId.length) {
            filter["roomId"] = { $in: roomId.map((_id) => new Types.ObjectId(_id)) };
        }
        if (trainerId.length) {
            filter["trainerId"] = { $in: trainerId.map((_id) => new Types.ObjectId(_id)) };
        }
        if (startDate && endDate) {
            filter["date"] = {
                $gte: startDate,
                $lte: endDate,
            };
        }
        const agg: PipelineStage[] = [
            {
                $match: filter,
            },
            {
                $lookup: {
                    from: "users",
                    localField: "clientId",
                    foreignField: "_id",
                    as: "client",
                    pipeline: [
                        {
                            $project: {
                                name: 1,
                                firstNam: 1,
                                lastName: 1,
                                email: 1,
                                mobile: 1,
                                isActive: 1,
                            },
                        },
                    ],
                },
            },
            {
                $set: {
                    client: {
                        $arrayElemAt: ["$client", 0],
                    },
                },
            },
            ...(search
                ? [
                    {
                        $addFields: {
                            searched: { $regexMatch: { input: "$client.name", regex: search, options: "i" } },
                        },
                    },
                    {
                        $match: {
                            searched: true,
                        },
                    },
                ]
                : []),
            {
                $facet: {
                    total: [
                        {
                            $count: "count",
                        },
                    ],
                    scheduleIds: [
                        {
                            $sort: {
                                updatedAt: -1,
                            },
                        },
                        {
                            $skip: skip,
                        },
                        {
                            $limit: pageSize,
                        },
                        {
                            $project: {
                                _id: 1,
                            },
                        },
                    ],
                },
            },
            {
                $replaceRoot: {
                    newRoot: {
                        total: {
                            $arrayElemAt: ["$total.count", 0],
                        },
                        scheduleIds: "$scheduleIds",
                    },
                },
            },
        ];
        const idsList = await this.SchedulingModel.aggregate(agg);
        const { total = 0, scheduleIds } = idsList.length ? idsList[0] : { total: 0, scheduleIds: [] };
        const populateFields = [
            { path: "scheduledBy", select: "_id name firstName lastName email" },
            { path: "facilityId", select: "_id facilityName address profilePicture" },
            { path: "trainerId", select: "_id name firstName lastName email" },
            // { path: 'organizationId', select: '_id name email' },
            { path: "clientId", select: "_id firstName lastName email mobile isActive" },
            { path: "packageId", select: "_id name price expiredInDays durationUnit" },
            // { path: 'subTypeId', select: '_id name description classType' },
            { path: "serviceCategoryId", select: "_id name classType appointmentType isActive" },
            { path: "roomId", select: "_id roomName capacity description" },
        ];
        const schedules = await this.SchedulingModel.find({ _id: { $in: scheduleIds.map((item) => item._id) } })
            .sort({ updatedAt: -1 })
            .populate(populateFields);
        const enrichedSchedules = schedules.map((schedule: any) => {
            const appointmentTypes = schedule?.serviceCategoryId?.appointmentType || [];
            const matchedAppointment = appointmentTypes.find((apt: any) => apt._id.toString() === schedule.subTypeId?.toString());

            return {
                ...schedule.toObject(),
                subTypeName: matchedAppointment?.name || null,
                subTypeId: matchedAppointment?._id || null,
            };
        });
        const userIds = enrichedSchedules?.filter((item: any) => item.clientId?._id).map((item: any) => item.clientId?._id);
        const clients = await this.ClientsModel.find({ userId: { $in: userIds } }, { userId: 1, clientId: 1 });
        // const customerId = await clients.find(client=>(client.userId == item.clientId._id)),
        const data = enrichedSchedules?.map((item: any) => {
            let customerId: any = clients.find((client) => client.userId.toString() == item.clientId?._id?.toString());
            customerId = customerId ? customerId.clientId : "";
            return {
                _id: item._id,
                facilityId: item.facilityId._id,
                trainerId: item.trainerId ? item.trainerId._id : "",
                trainerName: item.trainerId ? `${item.trainerId.firstName} ${item.trainerId.lastName}` : "",
                facilityName: item.facilityId.facilityName,
                clientId: item.clientId?._id,
                clientName: item.clientId?.firstName ? `${item.clientId?.firstName} ${item.clientId?.lastName}` : null,
                clientFirstName: item.clientId?.firstName,
                clientLastName: item.clientId?.lastName,
                clientEmail: item.clientId?.email,
                clientPhone: item.clientId?.mobile,
                customerId: customerId,
                packageId: item.packageId._id,
                packageName: item.packageId.name,
                classType: item.classType,
                scheduleStatus: item.scheduleStatus,
                serviceCategoryId: item.serviceCategoryId._id,
                serviceCategoryName: item.serviceCategoryId.name,
                subTypeId: item.subTypeId?._id,
                subTypeName: item.subTypeName,
                duration: item.duration,
                sessions: item.sessions,
                room: item.roomId ? item.roomId._id : null,
                roomName: item.roomId ? item.roomId.roomName : "",
                date: item.date,
                from: item.from,
                to: item.to,
            };
        });

        return {
            count: total,
            data,
        };
    }

    async cancelSchedule(user: IUserDocument, scheduleId: string): Promise<any> {
        const query = { _id: scheduleId }
        const { role } = user
        switch (role.type) {
            case ENUM_ROLE_TYPE.USER:
                query['clientId'] = user._id
                break
            case ENUM_ROLE_TYPE.ORGANIZATION:
                query['organizationId'] = user._id
                break
            case ENUM_ROLE_TYPE.WEB_MASTER:
            case ENUM_ROLE_TYPE.FRONT_DESK_ADMIN:
                const staffDetails = await this.StaffProfileModel.findOne(
                    { userId: user._id },
                    { organizationId: 1 }
                );
                if (!staffDetails) throw new BadRequestException("Access denied, Staff does not have access");
                query['organizationId'] = staffDetails.organizationId
                break
            case ENUM_ROLE_TYPE.TRAINER:
                const trainerDetails = await this.StaffProfileModel.findOne(
                    { userId: user._id },

                    { organizationId: 1 },
                );

                if (!trainerDetails) throw new BadRequestException("Access denied, Staff does not have access");

                query["organizationId"] = trainerDetails.organizationId;

                query["trainerId"] = user._id;
                break;
            default:
                throw new BadRequestException("Access denied");
        }

        const schedule = await this.SchedulingModel.findOne(query).populate("clientId facilityId packageId roomId");
        if (!schedule) {
            throw new NotFoundException(`Schedule with ID ${scheduleId} not found`);
        }

        if (role.type === ENUM_ROLE_TYPE.USER && schedule.scheduledBy.toString() !== user._id.toString()) {
            throw new BadRequestException("You are not authorized to cancel this schedule");
        }

        if (schedule.scheduleStatus === ScheduleStatusType.CANCELED) {
            throw new BadRequestException("This schedule is already cancelled");
        }

        if (schedule.scheduleStatus === ScheduleStatusType.CHECKEDIN) {
            throw new BadRequestException("This schedule is already checked-in");
        }

        const sessionStartDateTime = moment(schedule.date).format("YYYY-MM-DD") + " " + schedule.from;
        const currentDateTime = moment();

        if (currentDateTime.isAfter(moment(sessionStartDateTime, "YYYY-MM-DD HH:mm"))) {
            throw new BadRequestException("You can only cancel the session before it starts");
        }

        schedule.scheduleStatus = ScheduleStatusType.CANCELED;
        schedule.canceledBy = user._id;
        await schedule.save();
        await this.PurchaseModel.updateOne({ _id: schedule.purchaseId }, { $inc: { sessionConsumed: -1 * schedule.sessions } });
        this.waitTimeGatewayService.sendWaitingTimeUpdate(schedule.facilityId);

        return {
            scheduleStatus: schedule.scheduleStatus,
        };
    }

    async checkInSchedule(user: IUserDocument, scheduleId: string): Promise<any> {
        const query = { _id: scheduleId }
        const { role } = user
        switch (role.type) {
            case ENUM_ROLE_TYPE.USER:
                query['clientId'] = user._id
                break
            case ENUM_ROLE_TYPE.ORGANIZATION:
                query['organizationId'] = user._id
                break
            case ENUM_ROLE_TYPE.WEB_MASTER:
            case ENUM_ROLE_TYPE.FRONT_DESK_ADMIN:
                const staffDetails = await this.StaffProfileModel.findOne(
                    { userId: user._id },
                    { organizationId: 1 }
                );
                if (!staffDetails) throw new BadRequestException("Access denied, Staff does not have access");
                query['organizationId'] = staffDetails.organizationId
                break
            case ENUM_ROLE_TYPE.TRAINER:
                const trainerDetails = await this.StaffProfileModel.findOne(
                    { userId: user._id },

                    { organizationId: 1 },
                );

                if (!trainerDetails) throw new BadRequestException("Access denied, Staff does not have access");

                query["organizationId"] = trainerDetails.organizationId;

                query["trainerId"] = user._id;

                break;

            default:
                throw new BadRequestException("Access denied");
        }

        const schedule = await this.SchedulingModel.findOne(query).populate("clientId facilityId packageId roomId");
        if (!schedule) {
            throw new NotFoundException(`Schedule with ID ${scheduleId} not found`);
        }

        if (schedule.scheduleStatus === ScheduleStatusType.CHECKEDIN) {
            throw new BadRequestException("This session has already been checked in");
        }
        if (schedule.scheduleStatus === ScheduleStatusType.CANCELED) {
            throw new BadRequestException("This session has been canceled and cannot be checked in");
        }

        const sessionStartDate = new Date(schedule.date).setHours(0, 0, 0, 0);
        const currentDate = new Date().setHours(0, 0, 0, 0);
        const timeString = new Date().toLocaleTimeString("en-GB", { hour: "2-digit", minute: "2-digit" });
        if (currentDate > sessionStartDate || (currentDate == sessionStartDate && timeString > schedule.to)) {
            throw new BadRequestException("You can only check-in before or at the scheduled start time");
        }

        schedule.scheduleStatus = ScheduleStatusType.CHECKEDIN;
        await schedule.save();

        const updatedSchedule = await this.SchedulingModel.findById(schedule._id).populate([
            { path: "scheduledBy", select: "_id name firstName lastName email" },
            { path: "facilityId", select: "_id facilityName address profilePicture" },
            { path: "trainerId", select: "_id name firstName lastName email" },
            { path: "organizationId", select: "_id name email" },
            { path: "clientId", select: "_id firstName lastName email" },
            { path: "packageId", select: "_id name price expiredInDays durationUnit" },
            { path: "subTypeId", select: "_id name description classType" },
            { path: "serviceCategoryId", select: "_id name attributeType isActive" },
            { path: "roomId", select: "_id roomName capacity description" },
        ]);
        this.waitTimeGatewayService.sendWaitingTimeUpdate(schedule.facilityId);

        return {
            scheduleId: updatedSchedule._id,
            clientId: updatedSchedule.clientId,
            status: updatedSchedule.scheduleStatus,
            checkInTime: new Date(),
        };
    }
}
