import { BadRequestException, Body, Controller, Get, Param, Patch, Post } from "@nestjs/common";
import { SchedulingService } from "../services/scheduling.service";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { ApiBearerAuth, ApiOperation, ApiTags } from "@nestjs/swagger";
import { GetUser } from "src/auth/decorators/get-user.decorator";
import { CreateSchedulingDto } from "../dto/create-scheduling.dto";
import { UpdateSchedulingDto } from "../dto/update-scheduling.dto";
import { ClassType } from "src/utils/enums/class-type.enum";
import { CheckStaffAvailabilityDto } from "../dto/check-staff-availability.dto";
import { GetSchedulesDto } from "../dto/get-scheduling.dto";
import { IUserDocument } from "src/users/interfaces/user.interface";
import { AuthJwtAccessProtected } from "src/auth/decorators/auth.jwt.decorator";
import { Response } from "src/common/response/decorators/response.decorator";
import { PolicyAbilityProtected, PolicyAbilityRoleProtected } from "src/policy/decorators/policy.decorator";
import { ENUM_PERMISSION_TYPE } from "src/policy/enums/policy.permissions.enum";

@ApiTags("Scheduling")
@ApiBearerAuth()
@Controller("scheduling")
export class SchedulingController {
    constructor(private schedulingService: SchedulingService) {}

    @Response("scheduling.create.personalAppointment")
    // @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.SCHEDULING_SCHEDULE_PERSONAL_APPOINTMENT)
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Schedule Personal appointment" })
    @Post("/personal-appointment")
    async schedulingPersonalAppointment(
        @GetUser() user: any, 
        @Body() createSchedulingDto: CreateSchedulingDto
    ): Promise<any> {
        let classType = createSchedulingDto.classType;
        let data = null;
        if (classType === ClassType.PERSONAL_APPOINTMENT) {
            // Doing this
            data = await this.schedulingService.schedulePersonalAppointment(createSchedulingDto, user)
        } else {
            throw new BadRequestException("Invalid class type");
        }
        
        return {
            data: data,
        };
    }

    @Response('scheduling.create.session')
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.SCHEDULING_SCHEDULE_BOOKING)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Schedule session/booking" })
    @Post("/booking")
    async scheduling(
        @GetUser() user: any, 
        @Body() createSchedulingDto: CreateSchedulingDto
    ): Promise<any> {
        let classType = createSchedulingDto.classType;
        let data = null
        if (classType === ClassType.BOOKINGS) {
            // Doing this
            data = await this.schedulingService.scheduleSession(createSchedulingDto, user)
        } else {
            throw new BadRequestException("Invalid class type");
        }
        
        return {
            data: data,
        };
    }

    @Response('scheduling.update.personalAppointment')
    @Patch("/personal-appointment/update")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Edit or update personal appointment" })
    async editSchedulingPersonalAppointment(
        @GetUser() user: any, 
        @Body() updateSchedulingDto: UpdateSchedulingDto
    ): Promise<any> {
        let classType = updateSchedulingDto.classType;
        let data = null;
        if (classType === ClassType.PERSONAL_APPOINTMENT) {
            // Doing this
            data = await this.schedulingService.updatePersonalAppointment(updateSchedulingDto, user)
        } else {
            throw new BadRequestException("Invalid class type");
        }
        return {
            data: data,
        };
    }

    @Response('scheduling.update.session')
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.SCHEDULING_UPDATE_BOOKING)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Edit or update session/booking" })
    @Patch("/booking/update")
    async editSchedulingBooking(
        @GetUser() user: any, 
        @Body() updateSchedulingDto: UpdateSchedulingDto
    ): Promise<any> {
        let classType = updateSchedulingDto.classType;
        let data = null

        if (classType === ClassType.BOOKINGS) {
            data = await this.schedulingService.updateSession(updateSchedulingDto, user)
        } else {
            throw new BadRequestException("Invalid class type");
        }
        return {
            message: "Scheduled updated successfully",
            data: data,
        };
    }

    @Post("/get/list")
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.SCHEDULING_SCHEDULE_READ)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Get scheduling list" })
    async getSchedulingList(@GetUser() user: IUserDocument, @Body() body: GetSchedulesDto): Promise<any> {
        const schedules = await this.schedulingService.getSchedulesList(user, body);
        const totalPages = Math.ceil(schedules.count / body.pageSize);

        return {
            message: "Schedule fetched successfully",
            totalCount: schedules.count,
            totalPages: totalPages,
            page: body.page,
            data: schedules.data
        }
    }

    @Post("/get/list/v1")
   @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.SCHEDULING_SCHEDULE_READ)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Edit or update scheduling" })
    async getSchedulesListV1(@GetUser() user: any, @Body() body: GetSchedulesDto): Promise<any> {
        const schedules = await this.schedulingService.getSchedulesListV1(user, body);
        const totalPages = Math.ceil(schedules.count / body.pageSize);
        
        return {
            message: "Schedule fetched successfully",
            totalCount: schedules.count,
            totalPages: totalPages,
            page: body.page,
            data: schedules.data,
        };
    }

    @Get("/get/:scheduleId")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Edit or update scheduling" })
    async getSchedulingDetails(@GetUser() user: any, @Param("scheduleId") scheduleId: string): Promise<any> {
        const data = await this.schedulingService.getScheduleDetails(user, scheduleId);
        return {
            message: "Schedule fetched successfully",
            data,
        };
    }

    @Patch("/cancel/:scheduleId")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Edit or update scheduling" })
    async cancelSchedule(@GetUser() user: any, @Param("scheduleId") scheduleId: string): Promise<any> {
        const data = await this.schedulingService.cancelSchedule(user, scheduleId);
        return {
            message: "Schedule canceled successfully",
            data,
        };
    }

    @Patch("/checkIn/:scheduleId")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Edit or update scheduling" })
    async checkInSchedule(@GetUser() user: any, @Param("scheduleId") scheduleId: string): Promise<any> {
        const data = await this.schedulingService.checkInSchedule(user, scheduleId);
        return {
            message: "Checked-in successfully",
            data,
        };
    }

    // can be used anywhere for checking if staff is available or not in given date range and time and day
    @Post("/staff-availability")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Available Locations for class scheduling" })
    async checkStaffAvailability(@GetUser() user, @Body() checkStaffAvailabilityDto: CheckStaffAvailabilityDto): Promise<any> {
        let role = user.role;
        let organizationId;
        switch (role.type) {
            case ENUM_ROLE_TYPE.ORGANIZATION:
                organizationId = user._id;
                break;
            case ENUM_ROLE_TYPE.WEB_MASTER:
            case ENUM_ROLE_TYPE.FRONT_DESK_ADMIN:
                organizationId = await this.schedulingService.findOrganization(checkStaffAvailabilityDto);
                break;
            default:
                throw new BadRequestException("Access denied");
        }
        let classType = checkStaffAvailabilityDto.classType;
        let data;
        switch (classType) {
            case ClassType.CLASSES:
                data = await this.schedulingService.availableStaff(checkStaffAvailabilityDto, organizationId);
                break;
            default:
                throw new BadRequestException("Access denied");
        }
        return {
            message: "Class scheduled successfully",
            data: data,
        };
    }
}
