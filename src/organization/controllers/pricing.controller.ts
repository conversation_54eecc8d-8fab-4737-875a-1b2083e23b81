import { BadRequestException, Body, Controller, Delete, Get, Param, Patch, Post, UseGuards } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiTags } from "@nestjs/swagger";
import { PricingService } from "../services/pricing.service";
import { AuthGuard } from "@nestjs/passport";
import { RolesGuard } from "src/auth/roles.guard";
import { Roles } from "src/auth/decorators/roles.decorator";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { CreatePricingDto } from "../dto/create-pricing.dto";
import { GetDelegatedUser, GetUser } from "src/auth/decorators/get-user.decorator";
import { PricingListDto } from "../dto/pricing-list.dto";
import { EditPricingDto } from "../dto/edit-pricing.dto";
import { PricingListByAppointmentTypeDto } from "../dto/pricing-list-by-appointment-type.dto";
import { PricingListBySubTypeDto } from "../dto/pricing-list-by-subType.dto";
import { GetPricingDto } from "../dto/get-pricing.dto";
import { ClassType } from "src/utils/enums/class-type.enum";
import { PaginationDTO } from "src/users/dto/pagination.dto";
import { ChangeStatusDto } from "../dto/change-status.dto";
import { CreateBundledPricingDto } from "../dto/create-bundle-pricing.dto";
import { UpdateBundledPricingDto } from "../dto/update-bundle-pricing.dto";
import { GetPricingActiveListDto } from "../dto/get-pricing-active-list.dto";
import { CopyPricingDto } from "src/organization/dto/copy-pricing.dto";
import { GetPricingByServiceSubtypeDto } from "../dto/pricingBy-service-subtype.dto";
import { GetServicesByPackageDTO } from "../dto/getService-package.dto";
import { GetPricingForSharePassDto } from "src/organization/dto/get-pricing-list-for-sharepass.dto";
import { IUserDocument } from "src/users/interfaces/user.interface";
import { PolicyAbilityProtected, PolicyAbilityRoleProtected } from "src/policy/decorators/policy.decorator";
import { ENUM_PERMISSION_TYPE } from "src/policy/enums/policy.permissions.enum";
import { AuthJwtAccessProtected } from "src/auth/decorators/auth.jwt.decorator";
import { GetOrganizationId } from "../decorators/organization.decorator";
import { IDatabaseObjectId } from "src/common/database/interfaces/database.objectid.interface";

@ApiTags("pricing")
@ApiBearerAuth()
@Controller("pricing")
export class PricingController {
    constructor(private pricingService: PricingService) {}

    @Post('/')
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PRICING_WRITE)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Create a new Pricing" })
    async createPricing(
        @GetDelegatedUser() delegateUser: IUserDocument,
        @Body() createPricingDto: CreatePricingDto,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ): Promise<any> {
        const data = await this.pricingService.createPricingByStaff(createPricingDto, delegateUser?._id, organizationId);
        return {
            message: "Pricing created",
            data: data,
        };
    }

    @Post("/copy")
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PRICING_WRITE)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Copy Pricing" })
    async copyPricing(@Body() copyPricingDto: CopyPricingDto): Promise<any> {
        let data = await this.pricingService.copyPricing(copyPricingDto);
        return {
            message: "Pricing copy created",
            data: data,
        };
    }

    @Post("/list")
    // @UseGuards(AuthGuard(), RolesGuard)
    // @Roles(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN)
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PRICING_READ)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Pricing list" })
    async addOrganizationServices(@GetUser() user, @Body() pricingListDto: PricingListDto): Promise<any> {
        let data = "";
        const { role } = user;
        if (role.type === ENUM_ROLE_TYPE.ORGANIZATION) {
            data = await this.pricingService.pricingListByOrg(pricingListDto, user._id);
        } else {
            data = await this.pricingService.pricingListByStaff(pricingListDto, user._id);
        }
        return {
            message: "Pricing list",
            data: data,
        };
    }

    @Post("/list-by-service-category")
    // @UseGuards(AuthGuard(), RolesGuard)
    // @Roles(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN)
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PRICING_READ)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Pricing list by service category" })
    async pricingListByServiceCategory(@Body() pricingListDto: PricingListByAppointmentTypeDto): Promise<any> {
        let data = await this.pricingService.pricingListByService(pricingListDto);
        return {
            message: "Pricing list by service category",
            data: data,
        };
    }

    @Patch("/")
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PRICING_UPDATE)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Edit pricing" })
    async editOrganizationServices(
        @GetUser() user,
        @GetOrganizationId() organizationId: IDatabaseObjectId,
        @Body() updatePricingDto: EditPricingDto
    ): Promise<any> {
        const data = await this.pricingService.updatePricingByStaff(updatePricingDto, user._id, organizationId);
        return {
            message: "Pricing updated successfully",
            data: data,
        };
    }

    @Get("/:pricingId")
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PRICING_READ)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Get details of Pricing" })
    async getOrganizationServices(@Param("pricingId") pricingId: string): Promise<any> {
        let data = await this.pricingService.pricingDetails(pricingId);
        if (!data) throw new BadRequestException("Pricing not found");
        return {
            message: "Pricing details",
            data: data,
        };
    }

    @Delete("/:pricingId")

    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PRICING_DELETE)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Get details of Pricing" })
    async deletePricing(@Param("pricingId") pricingId: string): Promise<any> {
        let data = await this.pricingService.deletePricing(pricingId);
        if (!data) throw new BadRequestException("Pricing not found");
        return data;
    }

    @Get("get/:userId")
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PRICING_READ)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Get pricing list by User" })
    async getStaffServices(@GetUser() user, @Param("userId") userId: string): Promise<any> {
        let data = await this.pricingService.pricingListByUser(userId, user, ClassType.PERSONAL_APPOINTMENT);
        return {
            message: "Pricing list by User",
            data: data,
        };
    }

    @Post("/pricingByUserAndType")
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Get pricing list by User" })
    async getPricingByClientService(@GetUser() user, @Body() body: GetPricingDto): Promise<any> {
        let data = await this.pricingService.pricingListByUser(body.userId, user, body.classType, body.staffId, body.isForSharePass || false);
        return {
            message: "Pricing list by User",
            data: data,
        };
    }

    @Post("/pricingForSharePass")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER, ENUM_ROLE_TYPE.USER)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Get pricing for share pass" })
    async getPricingForSharePass(@GetUser() user, @Body() body: GetPricingForSharePassDto): Promise<any> {
        let data = await this.pricingService.getPricingForSharePass(body, user);
        return {
            message: "Pricing list for share pass",
            data: data,
        };
    }

    @Get("get-services/:packageId")
    // @UseGuards(AuthGuard(), RolesGuard)
    // @Roles(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.TRAINER, ENUM_ROLE_TYPE.USER, ENUM_ROLE_TYPE.WEB_MASTER,ENUM_ROLE_TYPE.FRONT_DESK_ADMIN)
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PRICING_READ)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Get Services list by PackageId" })
    async getServicesListByPackageId(@Param("packageId") packageId: string): Promise<any> {
        let data = await this.pricingService.getServicesListByPackageId(packageId);
        return {
            message: "Service list by PackageId",
            data: data,
        };
    }

    @Post("services-by-pricingId")
    // @UseGuards(AuthGuard(), RolesGuard)
    // @Roles(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.TRAINER, ENUM_ROLE_TYPE.USER, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN)
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PRICING_READ)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Get Services list by PackageId" })
    async getServicesListByPackageIdV1(@Param("packageId") packageId: string, @Body() getServicesByPackageDTO: GetServicesByPackageDTO): Promise<any> {
        let data = await this.pricingService.getServicesListByPackageIdV1(getServicesByPackageDTO);
        return {
            message: "Group sub type list fetched successfully",
            data: data,
        };
    }

    @Post("/list-by-subType")
    // @UseGuards(AuthGuard(), RolesGuard)
    // @Roles(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN)
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PRICING_READ)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Pricing list by Sub type" })
    async pricingListBySubType(@Body() pricingListDto: PricingListBySubTypeDto): Promise<any> {
        let data = await this.pricingService.pricingListBySubType(pricingListDto);
        return {
            message: "Pricing list by service category",
            data: data,
        };
    }

    @Post("/active-list")
    // @UseGuards(AuthGuard(), RolesGuard)
    // @Roles(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN)
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PRICING_READ)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Pricing list" })
    async activePriceList(
        @GetOrganizationId() organizationId: IDatabaseObjectId,
        @GetUser() user: IUserDocument, 
        @Body() paginationDto: PaginationDTO
    ): Promise<any> {
        let data = "";
        const { role } = user;
        if (role.type === ENUM_ROLE_TYPE.ORGANIZATION) {
            data = await this.pricingService.activePricingListByOrg(paginationDto, user._id);
        } else {
            data = await this.pricingService.activePricingListByUser(organizationId, paginationDto, user._id.toString());
        }
        return {
            message: "Pricing list",
            data: data,
        };
    }

    @Patch("/status/:packageId")

    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PRICING_WRITE)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Update pricing status" })
    async updatePricingStatus(@GetUser() user, @Param("packageId") packageId, @Body() updatePricingDto: ChangeStatusDto): Promise<any> {
        let role = user.role;
        let data = await this.pricingService.pricingStatusUpdate(updatePricingDto, user, packageId);
        return {
            message: "Pricing status updated",
            data: data,
        };
    }

    @Post("/bundled-pricing")
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PRICING_WRITE)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Create a new Bundled Pricing" })
    async createBundledPricing(@GetUser() user, @Body() createBundledPricingDto: CreateBundledPricingDto): Promise<any> {
        let data = "";
        if (user.role.type === ENUM_ROLE_TYPE.ORGANIZATION) {
            data = await this.pricingService.createBundledPricingByOrg(createBundledPricingDto, user._id);
        } else {
            data = await this.pricingService.createBundledPricingByStaff(createBundledPricingDto, user._id);
        }
        return {
            message: "Bundled Pricing created",
            data: data,
        };
    }

    @Post("/list/bundled-pricing")
    // @UseGuards(AuthGuard(), RolesGuard)
    // @Roles(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN)
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PRICING_READ)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Pricing list" })
    async listBundledPricing(@GetUser() user, @Body() pricingListDto: PricingListDto): Promise<any> {
        let data = "";
        if (user.role.type === ENUM_ROLE_TYPE.ORGANIZATION) {
            data = await this.pricingService.bundledPricingListByOrg(pricingListDto, user._id);
        } else {
            data = await this.pricingService.bundledPricingListByStaff(pricingListDto, user._id);
        }
        return {
            message: "Pricing list",
            data: data,
        };
    }

    @Patch("/bundled-pricing")

    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PRICING_WRITE)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Edit pricing" })
    async updateBundledPricing(@GetUser() user, @Body() updateBundledPricingDto: UpdateBundledPricingDto): Promise<any> {
        let role = user.role;
        let data = "";
        switch (role.type) {
            case ENUM_ROLE_TYPE.ORGANIZATION:
                data = await this.pricingService.updateBundledPricingByOrg(updateBundledPricingDto, user._id);
                break;
            case ENUM_ROLE_TYPE.WEB_MASTER:
                data = await this.pricingService.updateBundledPricingByStaff(updateBundledPricingDto, user._id);
                break;
            default:
                throw new BadRequestException("Invalid role");
        }
        return {
            message: "Bundled Pricing updated successfully",
            data: data,
        };
    }

    @Post("/pricingByUserAndSubType")
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PRICING_READ)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Pricing list by user and sub type" })
    async pricingByUserAndSubType(@GetUser() user, @Body() pricingListDto: GetPricingByServiceSubtypeDto): Promise<any> {
        let data = await this.pricingService.pricingByUserAndSubType(pricingListDto, user);
        return {
            message: "Pricing list fetched successfully",
            data: data,
        };
    }

    @Post("app/active-list")
    @ApiOperation({ summary: "Pricing list" })
    async activePriceListForApp(@Body() body: GetPricingActiveListDto): Promise<any> {
        let data = "";
        data = await this.pricingService.activePricingListByUserForApp(body);
        return {
            message: "Pricing list",
            data: data,
        };
    }
}
