import { Injectable, BadRequestException, NotFoundException, forwardRef, Inject } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model, PipelineStage, Types } from "mongoose";
import { Room } from "../schema/room.schema";
import { Services } from 'src/organization/schemas/services.schema'
import { CreateRoomDto, RoomListDto, RoomListByFacilitiesDto } from "../dto/room.dto";
import { TransactionService } from "src/utils/services/transaction.service";
import { Scheduling } from "src/scheduling/schemas/scheduling.schema";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { StaffProfileDetails } from "src/staff/schemas/staff.schema";
import { Facility } from "src/facility/schemas/facility.schema";
import moment from "moment-timezone";
import { WaitTimeGatewayService } from "src/wait-time/gateway/wait-time-gateway.service";
import { ScheduleStatusType } from "src/scheduling/enums/schedule-status.enum";
import { Clients } from "src/users/schemas/clients.schema";
import { GetRoomsForSchedulingDto } from "../dto/get-rooms.dto";
import { IUserDocument } from "src/users/interfaces/user.interface";
import { RoomAvailabilityDto } from "../dto/room-availability.dto";
import { FacilityAvailability } from "src/facility/schemas/facility-availability.schema";



@Injectable()
export class RoomService {
    constructor(
        @InjectModel(Room.name) private readonly roomModel: Model<Room>,
        @InjectModel(Services.name) private ServiceModel: Model<Services>,
        @InjectModel(Scheduling.name) private SchedulingModel: Model<Scheduling>,
        @InjectModel(StaffProfileDetails.name) private StaffProfileModel: Model<StaffProfileDetails>,
        @InjectModel(Facility.name) private FacilityModel: Model<Facility>,
        @InjectModel(Clients.name) private ClientModel: Model<Clients>,
        @InjectModel(FacilityAvailability.name) private FacilityAvailabilityModel: Model<FacilityAvailability>,
        @Inject(forwardRef(() => WaitTimeGatewayService))
        private waitTimeGatewayService: WaitTimeGatewayService,
        private readonly transactionService: TransactionService, // Transaction service for managing transactions
    ) { }


    private async getOrganizationId(user: any) {
        const { role } = user;
        let organizationId = null
        switch (role.type) {
            case ENUM_ROLE_TYPE.ORGANIZATION:
                organizationId = user._id
                break
            case ENUM_ROLE_TYPE.WEB_MASTER:
            case ENUM_ROLE_TYPE.FRONT_DESK_ADMIN:
            case ENUM_ROLE_TYPE.TRAINER:
                const staffDetails = await this.StaffProfileModel.findOne(
                    { userId: user._id },
                    { organizationId: 1 }
                );
                if (!staffDetails) throw new BadRequestException("Access denied, Staff does not have access");
                organizationId = staffDetails.organizationId
                break
            case ENUM_ROLE_TYPE.USER:
                const clientDetails = await this.ClientModel .findOne(
                    { userId: user._id },
                    { organizationId: 1 }
                );
                if (!clientDetails) throw new BadRequestException("Access denied, Client does not have access");
                organizationId = clientDetails.organizationId
                break
            default:
                throw new BadRequestException("Access denied");
        }
        return organizationId
    }

    /**
     * Create a new room
     * @param createRoomDto - Data to create the room
     * @returns The created room
     */
    async createRoom(createRoomDto: CreateRoomDto, user: any): Promise<{ message: string; data: Room }> {
        try {
            const organizationId = await this.getOrganizationId(user)
            createRoomDto["organizationId"] = organizationId
            const room = new this.roomModel(createRoomDto);
            await room.save();
            this.waitTimeGatewayService.sendWaitingTimeUpdate(createRoomDto.facilityId)
            return {
                message: "Room created successfully",
                data: room,
            };
        } catch (error) {
            console.error("Error creating room:", error.message); // Error log
            throw new BadRequestException("Failed to create room");
        }
    }

    /**
     * list all the room
     * @returns list all the room
     */


    async roomList(roomListDto: RoomListDto, user: IUserDocument): Promise<any> {
        const organizationId = await this.getOrganizationId(user)

        const pageSize = roomListDto.pageSize ?? 10;
        const page = roomListDto.page ?? 1;
        const skip = pageSize * (page - 1);
        const search = roomListDto.search || ""; // Search term
        const classSortType = roomListDto.classType;
        let query: any = {};
        if(ENUM_ROLE_TYPE.ORGANIZATION === user.role.type){
            let factlityIds=await this.FacilityModel.find({organizationId:user._id},{_id:1}).lean();
            query["facilityId"]={$in:factlityIds.map(item=>new Types.ObjectId(item._id))}
        }
        else if(ENUM_ROLE_TYPE.WEB_MASTER === user.role.type || ENUM_ROLE_TYPE.FRONT_DESK_ADMIN === user.role.type || ENUM_ROLE_TYPE.TRAINER === user.role.type){
            let staffDetails = await this.StaffProfileModel.findOne({ userId: user._id })
            query["facilityId"] = { $in: staffDetails.facilityId.map(item => new Types.ObjectId(item)) }
        }

        try {
            const aggregationPipeline: PipelineStage[] = [
                {
                    $match: {
                        facilityId: query.facilityId,
                        organizationId: organizationId, // Filter by user ID
                        ...(roomListDto?.facilityId && { facilityId: roomListDto?.facilityId }),
                        ...(roomListDto.status === true && { status: true }),
                        ...(search && {
                            $or: [
                                { roomName: { $regex: search, $options: "i" } }, // Case-insensitive search on roomName
                                { description: { $regex: search, $options: "i" } }, // Case-insensitive search on description
                            ],
                        }),
                        ...(classSortType && classSortType.length > 0 && {
                            classType: { $in: classSortType }, // Match any classType in the array
                        }),
                    },
                },
                {
                    $lookup: {
                        from: "facilities", // The collection name for the Facility model
                        localField: "facilityId",
                        foreignField: "_id",
                        as: "locationDetails",
                    },
                },
                {
                    $unwind: {
                        path: "$locationDetails",
                        preserveNullAndEmptyArrays: true, // Keeps rooms without matching facilities
                    },
                },
                {
                    $lookup: {
                        from: "services",
                        localField: "serviceCategory",
                        foreignField: "_id",
                        as: "serviceDetails",
                    },
                },
                {
                    $sort: { updatedAt: -1 }, // Sort by creation date (newest first)
                },
                {
                    $skip: skip, // Pagination: skip records
                },
                {
                    $limit: pageSize, // Pagination: limit number of records
                },
                {
                    $facet: {
                        data: [], // Processed documents
                        totalCount: [
                            { $count: "count" }, // Count total documents matching criteria
                        ],
                    },
                },
                {
                    $project: {
                        data: 1,
                        totalCount: { $arrayElemAt: ["$totalCount.count", 0] }, // Flatten count to a single value
                    },
                },
            ];
            const result = await this.roomModel.aggregate(aggregationPipeline).exec();
            return {
                list: result[0]?.data || [],
                count: result[0]?.totalCount || 0,
            };
        } catch (error) {
            console.error("Error in roomList aggregation:", error.message);
            throw new BadRequestException("Failed to fetch rooms");
        }
    }

    async roomById(roomId: string, user: any): Promise<any> {
        const organizationId = await this.getOrganizationId(user)
        try {
            const result = await this.roomModel.findOne({ _id: roomId, organizationId: organizationId })
                .populate({
                    path: 'serviceCategory', // Field to populate
                    model: 'Services',       // Correct model name
                }).lean();
            return result;
        } catch (error) {
            console.log(error)
        }
    }
    async roomUpdate(updateRoomDto: CreateRoomDto, roomId: string, user: string): Promise<any> {
        const session = await this.transactionService.startTransaction();
        const organizationId = await this.getOrganizationId(user);
        try {
            let roomUpdateData = {
                roomName: updateRoomDto.roomName,
                capacity: updateRoomDto.capacity,
                status: updateRoomDto.status,
                facilityId: updateRoomDto.facilityId,
                classType: updateRoomDto.classType,
                description: updateRoomDto.description,
                serviceCategory: updateRoomDto.serviceCategory
            }
            const updateRoom = await this.roomModel.updateOne({
                _id: roomId,
                organizationId: organizationId
            },
                {
                    $set: roomUpdateData,
                },
                {
                    new: true,
                    session,
                },
            )
            if (!updateRoom) throw new BadRequestException("Room not found");
            await this.transactionService.commitTransaction(session);

            this.waitTimeGatewayService.sendWaitingTimeUpdate(updateRoomDto.facilityId);

            return updateRoom;
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }
    async delete(id: string): Promise<any> {
        const session = await this.transactionService.startTransaction();
        try {
            const isRoomAllocated = await this.SchedulingModel.findOne({ roomId: id });

            if (isRoomAllocated) {
                throw new NotFoundException("Room is already linked to a booking and cannot be deleted.");
            }

            const deletedRoom = await this.roomModel.findByIdAndDelete(id).session(session);

            if (!deletedRoom) {
                throw new NotFoundException(`Room with ID ${id} not found`);
            }

            await this.transactionService.commitTransaction(session);

            this.waitTimeGatewayService.sendWaitingTimeUpdate(deletedRoom.facilityId);

            return deletedRoom;
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }
    async updateRoomStatus(isActive: any, roomId: string) {
        const session = await this.transactionService.startTransaction();
        try {
            const currentISTTime = moment().tz("Asia/Kolkata").format("HH:mm");
            const startOfDay = moment().startOf("day").toDate();
            const startOfNextDay = moment().add(1, 'day').startOf('day').toDate();
            const isRoomAllocated = await this.SchedulingModel.findOne({
                $or: [
                    {
                        date: { $gt: startOfDay },
                        $or: [
                            { from: { $gt: currentISTTime } },
                            { to: { $gt: currentISTTime } }
                        ]
                    },
                    {
                        date: { $gt: startOfNextDay }
                    }
                ],
                scheduleStatus: { $in: [ScheduleStatusType.BOOKED, ScheduleStatusType.CHECKEDIN] },
                roomId: roomId
            });

            if (isRoomAllocated) {
                throw new NotFoundException("This room has an active booking, status cannot be changed.");
            }
            const roomDetails = await this.roomModel.findById(roomId).exec()

            const updateResult = await this.roomModel.updateOne(
                { _id: roomId },
                { $set: { status: isActive.isActive || false } },
                { session }
            );

            if (updateResult.matchedCount === 0) {
                throw new BadRequestException("Room not found");
            }

            await this.transactionService.commitTransaction(session);

            this.waitTimeGatewayService.sendWaitingTimeUpdate(roomDetails.facilityId);

            return updateResult;
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }

    async roomListByServiceId(payload: any, user: any): Promise<any> {
        const organizationId = await this.getOrganizationId(user)
        if (!user || !user._id) throw new BadRequestException("Invalid user");

        const { serviceCategoryId, facilityId } = payload;
        try {
            // Validate input
            if (!serviceCategoryId) throw new BadRequestException("Service category ID is required");
            if (!facilityId) throw new BadRequestException("Facility ID is required");
            // MongoDB aggregation pipeline
            const rooms = await this.roomModel.aggregate([
                {
                    $match: {
                        facilityId: new Types.ObjectId(facilityId),
                        serviceCategory: {
                            $in: [new Types.ObjectId(serviceCategoryId)],
                        },
                        organizationId: organizationId,
                        status: true
                    },
                },
                {
                    $lookup: {
                        from: "facilities",
                        localField: "facilityId",
                        foreignField: "_id",
                        as: "facilityDetails",
                    },
                },
                {
                    $unwind: {
                        path: "$facilityDetails",
                        preserveNullAndEmptyArrays: true,
                    },
                },
                {
                    $lookup: {
                        from: "services",
                        localField: "serviceCategory",
                        foreignField: "_id",
                        as: "serviceDetails",
                    },
                },
                {
                    $sort: { updatedAt: -1 },
                },
                {
                    $project: {
                        _id: 1,
                        roomName: 1,
                        facilityId: 1,
                        facilityDetails: {
                            name: 1,
                            location: 1,
                        },
                        serviceDetails: {
                            _id: 1,
                            name: 1,
                        },
                        classType: 1,
                    },
                },
            ]);



            return rooms;
        } catch (error) {
            console.error("Error in roomList aggregation:", error.message);
            throw new BadRequestException("Failed to fetch rooms");
        }
    }
    async roomListByFacilitiesId(payload: RoomListByFacilitiesDto, user: any): Promise<any> {
        if (!user || !user._id) throw new BadRequestException("Invalid user");
        const organizationId = await this.getOrganizationId(user)
        const { serviceCategoryId, facilityId, classType, roomIds } = payload;
        try {
            // Validate input
            if (!facilityId) throw new BadRequestException("Facility ID is required");

            const matchCondition: any = {
                facilityId: new Types.ObjectId(facilityId),
                organizationId: organizationId,
                status: true,
            };
            if (serviceCategoryId && Array.isArray(serviceCategoryId) && serviceCategoryId.length > 0) {
                matchCondition.serviceCategory = { $in: serviceCategoryId.map(id => new Types.ObjectId(id)) };
            }

            if (classType && Array.isArray(classType) && classType.length > 0) {
                matchCondition.classType = { $in: classType };;
            }
            if (roomIds && Array.isArray(roomIds) && roomIds.length > 0) {
                matchCondition._id = { $in: roomIds.map(id => new Types.ObjectId(id)) }; // Add roomId match condition
            }

            // MongoDB aggregation pipeline
            const rooms = await this.roomModel.aggregate([
                {
                    $match: matchCondition,
                },
                {
                    $lookup: {
                        from: "facilities",
                        localField: "facilityId",
                        foreignField: "_id",
                        as: "facilityDetails",
                    },
                },
                {
                    $unwind: {
                        path: "$facilityDetails",
                        preserveNullAndEmptyArrays: true,
                    },
                },
                {
                    $lookup: {
                        from: "services",
                        localField: "serviceCategory",
                        foreignField: "_id",
                        as: "serviceDetails",
                    },
                },
                {
                    $sort: { updatedAt: -1 },
                },
                {
                    $project: {
                        _id: 1,
                        roomName: 1,
                        facilityId: 1,
                        facilityDetails: {
                            name: 1,
                            location: 1,
                        },
                        serviceDetails: {
                            _id: 1,
                            name: 1,
                        },
                        classType: 1,
                    },
                },
            ]);

            return rooms;
        } catch (error) {
            console.error("Error in roomList aggregation:", error.message);
            throw new BadRequestException("Failed to fetch rooms");
        }
    }

    async roomWaitTime(facilityId: string): Promise<any> {
        try {
            if (!facilityId || !Types.ObjectId.isValid(facilityId)) {
                throw new Error("Invalid facilityId provided.");
            }

            const currentISTTime = moment().tz("Asia/Kolkata").format("HH:mm");
            const currentISTDateStr = moment().tz("Asia/Kolkata").format("YYYY-MM-DD");

            const bookedRoomsPipeline = [
                {
                    $match: { facilityId: new Types.ObjectId(facilityId), status: true }
                },
                {
                    $lookup: {
                        from: "facilities",
                        localField: "facilityId",
                        foreignField: "_id",
                        as: "facility"
                    }
                },
                {
                    $lookup: {
                        from: "schedulings",
                        localField: "_id",
                        foreignField: "roomId",
                        as: "result"
                    }
                },
                {
                    $unwind: {
                        path: "$result",
                        preserveNullAndEmptyArrays: true
                    }
                },
                {
                    $addFields: {
                        "result.dateString": {
                            $dateToString: {
                                format: "%Y-%m-%d",
                                date: "$result.date",
                                timezone: "Asia/Kolkata"
                            }
                        }
                    }
                },
                {
                    $match: {
                        "result.dateString": currentISTDateStr,
                        "result.from": { $lte: currentISTTime },
                        "result.to": { $gt: currentISTTime },
                        "result.scheduleStatus": { $in: [ScheduleStatusType.BOOKED, ScheduleStatusType.CHECKEDIN] }
                    }
                },
                {
                    $group: {
                        _id: "$_id",
                        roomId: { $first: "$_id" },
                        roomName: { $first: "$roomName" },
                        facilityName: { $first: { $arrayElemAt: ["$facility.facilityName", 0] } }, // Extract facility name
                        capacity: { $first: "$capacity" },
                        bookings: { $push: "$result" }
                    }
                },
                {
                    $addFields: {
                        bookingsCount: { $size: "$bookings" },
                        soonestEndingBooking: {
                            $arrayElemAt: [
                                { $sortArray: { input: "$bookings", sortBy: { "to": 1 } } }, 0
                            ]
                        }
                    }
                },
                {
                    $project: {
                        _id: 1,
                        roomId: 1,
                        roomName: 1,
                        facilityName: 1,
                        capacity: 1,
                        bookingsCount: 1,
                        soonestEndingBooking: {
                            from: "$soonestEndingBooking.from",
                            to: "$soonestEndingBooking.to",
                            date: "$soonestEndingBooking.date",
                            duration: "$soonestEndingBooking.duration",
                            scheduleStatus: "$soonestEndingBooking.scheduleStatus"
                        }
                    }
                }
            ];

            const bookedRooms = await this.roomModel.aggregate(bookedRoomsPipeline);

            // Step 2: Process the booked rooms based on capacity
            const finalBookedRooms = bookedRooms.map(room => {
                if (room.bookingsCount >= room.capacity) {
                    return {
                        _id: room._id,
                        roomId: room.roomId,
                        roomName: room.roomName,
                        facilityName: room.facilityName,
                        from: room.soonestEndingBooking?.from,
                        to: room.soonestEndingBooking?.to,
                        date: room.soonestEndingBooking?.date,
                        duration: room.soonestEndingBooking?.duration,
                        scheduleStatus: room.soonestEndingBooking?.scheduleStatus,
                        isavailable: false
                    };
                } else {
                    return {
                        _id: room._id,
                        roomId: room.roomId,
                        roomName: room.roomName,
                        facilityName: room.facilityName,
                        isavailable: true
                    };
                }
            });

            const bookedRoomIds = finalBookedRooms.map(room => room.roomId);

            const availableRoomsPipeline = [
                {
                    $match: {
                        facilityId: new Types.ObjectId(facilityId),
                        _id: { $nin: bookedRoomIds },
                        status: true
                    }
                },
                {
                    $lookup: {
                        from: "facilities",
                        localField: "facilityId",
                        foreignField: "_id",
                        as: "facility"
                    }
                },
                {
                    $project: {
                        _id: 1,
                        roomId: "$_id",
                        roomName: 1,
                        facilityName: { $arrayElemAt: ["$facility.facilityName", 0] }, // Extract facility name
                        isavailable: { $literal: true }
                    }
                }
            ];

            const availableRooms = await this.roomModel.aggregate(availableRoomsPipeline);

            const finalResult = [...finalBookedRooms, ...availableRooms].sort((a, b) => a._id.toString().localeCompare(b._id.toString()));

            if (finalResult.length === 0) {
                return [];
            }

            return finalResult;

        } catch (error) {
            console.error("Error in roomWaitTime:", error.message);
            return { error: "Something went wrong. Please try again later." };
        }
    }

    async roomListForScheduling(user: any, reqBody: GetRoomsForSchedulingDto): Promise<any> {
        const organizationId = await this.getOrganizationId(user)
        if (!user || !user._id) throw new BadRequestException("Invalid user");

        let { facilityId, classType, serviceId, date, startTime, endTime } = reqBody;
        let startDate = new Date(date);
        const startOfDay = new Date(startDate.setHours(0, 0, 0, 0));
        const endOfDay = new Date(startDate.setHours(23, 59, 59, 999));
        if (startTime > endTime) {
            throw new BadRequestException("StartTime must be greater than end time");
        }
        let roomId = null;
        if (reqBody.scheduleId) {
            const schedule = await this.SchedulingModel.findOne({
                _id: new Types.ObjectId(reqBody.scheduleId),
                date,
                from: startTime,
                to: endTime,
                scheduleStatus: { $ne: ScheduleStatusType.CANCELED }
            });

            if (schedule) {
                roomId = schedule?.roomId;
            }
        }

        try {

            const bookedRoomsPipeline = [
                {
                    $match: {
                        $or: [
                            {
                                facilityId: new Types.ObjectId(facilityId),
                            },
                            {
                                facilityId: new Types.ObjectId(facilityId),
                                organizationId: new Types.ObjectId(organizationId),
                            }
                        ],
                        classType: { $in: [classType] },
                        serviceCategory: { $in: [new Types.ObjectId(serviceId)] },
                        status: true
                    },
                },
                {
                    $lookup: {
                        from: "schedulings",
                        localField: "_id",
                        foreignField: "roomId",
                        as: "scheduleDetails",
                    },
                },
                {
                    $unwind: {
                        path: "$scheduleDetails",
                        preserveNullAndEmptyArrays: true,
                    },
                },
                {
                    $match: {
                        $and: [
                            { "scheduleDetails.facilityId": new Types.ObjectId(facilityId) },
                            { "scheduleDetails.scheduleStatus": { $in: [ScheduleStatusType.BOOKED, ScheduleStatusType.CHECKEDIN] } },
                            { "scheduleDetails.date": { $gte: startOfDay } },
                            { "scheduleDetails.date": { $lte: endOfDay } },
                            { "scheduleDetails.from": { $lt: endTime } },
                            { "scheduleDetails.to": { $gt: startTime } },
                        ]
                    }
                },
                {
                    $group: {
                        _id: "$_id",
                        roomId: { $first: "$_id" },
                        roomName: { $first: "$roomName" },
                        capacity: { $first: "$capacity" },
                        bookings: { $push: "$scheduleDetails" }
                    }
                },
                {
                    $addFields: {
                        bookingsCount: { $size: "$bookings" },
                    }
                },
                {
                    $project: {
                        _id: 1,
                        roomId: 1,
                        roomName: 1,
                        capacity: 1,
                        bookingsCount: 1,
                    }
                }
            ];
            const bookedRooms = await this.roomModel.aggregate(bookedRoomsPipeline);

            let roomIds = []
            let bookedRoomsMap = new Map();

            if (bookedRooms.length > 0) {
                bookedRooms.map(room => {
                    if (room?.bookingsCount >= room?.capacity) {
                        roomIds.push(new Types.ObjectId(room._id));
                    }
                    bookedRoomsMap.set(room._id.toString(), room.bookingsCount);

                });
            }

            const roomsPipeline = [
                {
                    $match: {
                        $or: [
                            ...(roomId ? [{ _id: roomId }] : []),
                            {
                                facilityId: new Types.ObjectId(facilityId),
                                classType: { $in: [classType] },
                                serviceCategory: { $in: [new Types.ObjectId(serviceId)] },
                                status: true
                            }
                        ]
                    }
                },
                {
                    $lookup: {
                        from: "schedulings",
                        let: { roomId: "$_id" },
                        pipeline: [
                            {
                                $match: {
                                    $expr: {
                                        $and: [
                                            { $eq: ["$roomId", "$$roomId"] },
                                            { $eq: ["$facilityId", new Types.ObjectId(facilityId)] },
                                            { $in: ["$scheduleStatus", [ScheduleStatusType.BOOKED, ScheduleStatusType.CHECKEDIN]] },
                                            { $gte: ["$date", startOfDay] },
                                            { $lte: ["$date", endOfDay] },
                                            { $lt: ["$from", endTime] },
                                            { $gt: ["$to", startTime] }
                                        ]
                                    }
                                }
                            }
                        ],
                        as: "conflictingSchedules"
                    }
                },
                {
                    $addFields: {
                        bookingsCount: { $size: "$conflictingSchedules" },
                        isAvailable: {
                            $lt: [{ $size: "$conflictingSchedules" }, "$capacity"]
                        },
                        reason: {
                            $cond: {
                                if: { $gte: [{ $size: "$conflictingSchedules" }, "$capacity"] },
                                then: "Selected room is at full capacity at the selected time",
                                else: null
                            }
                        }
                    }
                },
                {
                    $project: {
                        _id: 1,
                        roomId: "$_id",
                        roomName: 1,
                        capacity: 1,
                        bookingsCount: 1,
                        isAvailable: 1,
                        reason: 1
                    }
                },
                { $sort: { roomName: 1 as 1 } }
            ];


            const allRoomsWithStatus = await this.roomModel.aggregate(roomsPipeline as PipelineStage[]);



            const finalResult = allRoomsWithStatus.map(room => ({
                ...room,
                bookingsCount: bookedRoomsMap.get(room._id.toString()) || 0
            })).sort((a, b) => a._id.toString().localeCompare(b._id.toString()));

            if (finalResult.length === 0) {
                return [];
            }
            return finalResult;
        } catch (error) {
            console.error("Error in roomList aggregation:", error.message);
            throw new BadRequestException("Failed to fetch rooms");
        }
    }

    async getAvailableRoomsByCriteria(dto: RoomAvailabilityDto): Promise<any> {
        const { facilityId, classType, serviceCategoryId, date } = dto;
        const dayOfWeek = moment(date).tz("Asia/Kolkata").format("ddd").toLowerCase();
        const startOfDay = moment(date).tz("Asia/Kolkata").startOf("day").toDate();
        const endOfDay = moment(date).tz("Asia/Kolkata").endOf("day").toDate();
        const currentISTTime = moment().tz("Asia/Kolkata").format("HH:mm");



        const facilityRecords = await this.FacilityAvailabilityModel.find({
            facilityId: new Types.ObjectId(facilityId),
            $or: [
                { type: "unavailable", fromDate: { $lte: endOfDay }, endDate: { $gte: startOfDay } },
                { type: "available" },
            ],
        }).lean();

        const unavailableRecord = facilityRecords.find(r => r.type === "unavailable");
        const facilityAvailability = facilityRecords.find(r => r.type === "available");
        const facilityWorkingHours = facilityAvailability?.workingHours?.[dayOfWeek] || [];
        const facilityNotAvailableHours = unavailableRecord?.time || [];


        try {
            const bookedRooms = await this.roomModel.aggregate([
                {
                    $match: {
                        facilityId: new Types.ObjectId(facilityId),
                        classType,
                        serviceCategory: new Types.ObjectId(serviceCategoryId),
                        status: true
                    }
                },
                {
                    $lookup: {
                        from: "schedulings",
                        let: { roomId: "$_id" },
                        pipeline: [
                            {
                                $match: {
                                    $expr: {
                                        $and: [
                                            { $eq: ["$roomId", "$$roomId"] },
                                            { $eq: ["$facilityId", new Types.ObjectId(facilityId)] },
                                            { $in: ["$scheduleStatus", [ScheduleStatusType.BOOKED, ScheduleStatusType.CHECKEDIN]] },
                                            { $gte: ["$date", startOfDay] },
                                            { $lte: ["$date", endOfDay] },
                                            { $gt: ["$to", currentISTTime] }
                                        ]
                                    }
                                }
                            }
                        ],
                        as: "bookings"
                    }
                },
                {
                    $addFields: { bookingsCount: { $size: "$bookings" } }
                },
                {
                    $project: {
                        _id: 1,
                        roomId: "$_id",
                        roomName: 1,
                        capacity: 1,
                        bookingsCount: 1,
                        bookings: {
                            $map: {
                                input: "$bookings",
                                as: "b",
                                in: { from: "$$b.from", to: "$$b.to" }
                            }
                        }
                    }
                }
            ]);
            const fullyBookedRoomIds = bookedRooms
                .filter(room => room.bookingsCount >= room.capacity)
                .map(room => room._id);
            const availableRooms = await this.roomModel.aggregate([
                {
                    $match: {
                        facilityId: new Types.ObjectId(facilityId),
                        _id: { $nin: fullyBookedRoomIds },
                        classType,
                        serviceCategory: new Types.ObjectId(serviceCategoryId),
                        status: true
                    }
                },
                {
                    $project: {
                        _id: 1,
                        roomId: "$_id",
                        roomName: 1,
                        capacity: 1,
                        isavailable: { $literal: true }
                    }
                }
            ]);
            if (availableRooms.length > 0) {

                return this.calculateAvailableHours(facilityWorkingHours, facilityNotAvailableHours);
            }

            if (bookedRooms.length > 0) {
                const roomNotAvailHours = bookedRooms.flatMap(room => room.bookings);

                return this.calculateAvailableHours(facilityWorkingHours, roomNotAvailHours);
            }

            return [];
        } catch (error) {
            console.error("Error in getAvailableRoomsByCriteria:", error.message);
            throw new BadRequestException("Failed to fetch rooms");
        }
    }

    private calculateAvailableHours(workingHours, notAvailableHours) {
        const availableSlots = [];

        const timeToMinutes = (time) => {
            const [hours, minutes] = time.split(':').map(Number);
            return hours * 60 + minutes;
        };

        const minutesToTime = (minutes) => {
            const hrs = Math.floor(minutes / 60);
            const mins = minutes % 60;
            return `${hrs.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
        };

        for (const workSlot of workingHours) {
            let workStart = timeToMinutes(workSlot.from);
            let workEnd = timeToMinutes(workSlot.to);

            const overlappingNotAvailable = notAvailableHours
                .map(na => ({
                    start: timeToMinutes(na.from),
                    end: timeToMinutes(na.to)
                }))
                .filter(na => na.end > workStart && na.start < workEnd)
                .sort((a, b) => a.start - b.start);

            if (overlappingNotAvailable.length === 0) {
                availableSlots.push({
                    from: workSlot.from,
                    to: workSlot.to
                });
                continue;
            }

            let currentStart = workStart;

            for (const na of overlappingNotAvailable) {
                if (na.start > currentStart) {
                    availableSlots.push({
                        from: minutesToTime(currentStart),
                        to: minutesToTime(na.start)
                    });
                }
                currentStart = Math.max(currentStart, na.end);
            }

            if (currentStart < workEnd) {
                availableSlots.push({
                    from: minutesToTime(currentStart),
                    to: workSlot.to
                });
            }
        }

        return availableSlots;
    }
}