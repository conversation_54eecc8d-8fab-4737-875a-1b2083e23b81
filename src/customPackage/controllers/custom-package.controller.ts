import { Body, Controller, Get, Param, Patch, Post, UseGuards } from "@nestjs/common";
import { CustomPackageService } from "../services/custom-package.service";
import { AuthGuard } from "@nestjs/passport";
import { RolesGuard } from "src/auth/roles.guard";
import { Roles } from "src/auth/decorators/roles.decorator";
import { ApiBearerAuth, ApiOperation, ApiTags } from "@nestjs/swagger";
import { GetUser } from "src/auth/decorators/get-user.decorator";
import { CreateCustomPackageDto, CustomPackageListDto, UpdateCustomPackageDto } from "src/customPackage/dto/custom-package.dto";
import { AuthJwtAccessProtected } from "src/auth/decorators/auth.jwt.decorator";
import { PolicyAbilityProtected } from "src/policy/decorators/policy.decorator";
import { ENUM_PERMISSION_TYPE } from "src/policy/enums/policy.permissions.enum";

@ApiTags("custom-package")
@ApiBearerAuth()
@Controller("custom-package")
export class CustomPackageController {
    constructor(private customPackageService: CustomPackageService) {}

    @Post("/create")
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PRICING_WRITE)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Create Custom Package" })
    async createCourseScheduling(@GetUser() user: any, @Body() createCustomPackageDto: CreateCustomPackageDto): Promise<any> {
        const output = await this.customPackageService.createCustomPackage(createCustomPackageDto, user);
        return output;
    }

    @Patch("/update")
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PRICING_UPDATE)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Update Custom Package" })
    async updateCourseScheduling(@GetUser() user: any, @Body() updateCustomPackageDto: UpdateCustomPackageDto): Promise<any> {
        const output = await this.customPackageService.updateCustomPackage(updateCustomPackageDto, user);
        return output;
    }

    @Post("/list")
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PRICING_READ)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Custom Package list" })
    async getSchedulingList(@Body() customPackageListDto: CustomPackageListDto): Promise<any> {
        const output = await this.customPackageService.getCustomPackageList(customPackageListDto);
        return output;
    }

    @Get("/details/:id")
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PRICING_READ)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Custom Package Details" })
    async getSchedulingDetails(@GetUser() user: any, @Param("id") id: string): Promise<any> {
        let output = await this.customPackageService.getCustomPackageDetails(user, id);
        return output;
    }
}
